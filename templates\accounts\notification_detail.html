{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ notification.title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accounts:notifications' %}">{% trans "الإشعارات" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ notification.title }}</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">
                {% if notification.priority == 'urgent' %}
                    <span class="badge bg-danger">{% trans "عاجل" %}</span>
                {% elif notification.priority == 'high' %}
                    <span class="badge bg-warning text-dark">{% trans "مهم" %}</span>
                {% elif notification.priority == 'medium' %}
                    <span class="badge bg-info text-dark">{% trans "متوسط" %}</span>
                {% else %}
                    <span class="badge bg-secondary">{% trans "عادي" %}</span>
                {% endif %}
                {{ notification.title }}
            </h4>
            <div>
                <a href="{% url 'accounts:notifications' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {% trans "العودة للإشعارات" %}
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>{% trans "معلومات الإشعار" %}</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">{% trans "المرسل" %}</th>
                            <td>{{ notification.sender.get_full_name|default:notification.sender.username }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "القسم المرسل" %}</th>
                            <td>{{ notification.sender_department.name }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "القسم المستهدف" %}</th>
                            <td>{{ notification.target_department.name }}</td>
                        </tr>
                        {% if notification.target_branch %}
                        <tr>
                            <th>{% trans "الفرع المستهدف" %}</th>
                            <td>{{ notification.target_branch.name }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>{% trans "تاريخ الإرسال" %}</th>
                            <td>{{ notification.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الحالة" %}</th>
                            <td>
                                {% if notification.is_read %}
                                    <span class="badge bg-success">{% trans "مقروءة" %}</span>
                                    {% if notification.read_by %}
                                        {% trans "بواسطة" %} {{ notification.read_by.get_full_name|default:notification.read_by.username }}
                                        {% trans "في" %} {{ notification.read_at|date:"Y-m-d H:i" }}
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-warning text-dark">{% trans "غير مقروءة" %}</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
                
                {% if notification.related_object %}
                <div class="col-md-6">
                    <h5>{% trans "الكائن المرتبط" %}</h5>
                    <div class="card">
                        <div class="card-body">
                            <h6>{{ notification.content_type.name }}</h6>
                            {% if notification.content_type.model == 'order' %}
                                <a href="{% url 'orders:order_detail' notification.object_id %}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> {% trans "عرض الطلب" %}
                                </a>
                            {% elif notification.content_type.model == 'productionorder' %}
                                <a href="{% url 'factory:production_order_detail' notification.object_id %}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> {% trans "عرض أمر الإنتاج" %}
                                </a>
                            {% elif notification.content_type.model == 'inspection' %}
                                <a href="{% url 'inspections:inspection_detail' notification.object_id %}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> {% trans "عرض المعاينة" %}
                                </a>
                            {% elif notification.content_type.model == 'installation' %}
                                <a href="{% url 'installations:installation_detail' notification.object_id %}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> {% trans "عرض التركيب" %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "محتوى الإشعار" %}</h5>
                </div>
                <div class="card-body">
                    <div class="notification-message">
                        {{ notification.message|linebreaks }}
                    </div>
                </div>
            </div>
            
            {% if not notification.is_read %}
            <div class="text-center">
                <button id="markAsReadBtn" class="btn btn-primary">
                    <i class="fas fa-check"></i> {% trans "تحديد كمقروء" %}
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if not notification.is_read %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const markAsReadBtn = document.getElementById('markAsReadBtn');
        if (markAsReadBtn) {
            markAsReadBtn.addEventListener('click', function() {
                // Send AJAX request to mark notification as read
                fetch('{% url "accounts:mark_notification_read" notification.id %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show updated status
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء تحديد الإشعار كمقروء.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحديد الإشعار كمقروء.');
                });
            });
        }
    });
</script>
{% endif %}
{% endblock %}
