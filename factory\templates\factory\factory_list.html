{% extends 'base.html' %}

{% block title %}إدارة المصنع - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">إدارة المصنع</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
            </a>
        </div>
    </div>

    <!-- Production Lines -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-industry"></i> خطوط الإنتاج</h5>
                    <a href="{% url 'factory:production_line_list' %}" class="btn btn-sm" style="background-color: white; color: var(--primary);">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if production_lines %}
                            {% for line in production_lines %}
                                <div class="col-md-4 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-cogs fa-3x mb-3" style="color: var(--primary);"></i>
                                            <h5 class="card-title">{{ line.name }}</h5>
                                            <p class="card-text">حالة الخط: 
                                                <span class="badge {% if line.is_active %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                                    {% if line.is_active %}نشط{% else %}غير نشط{% endif %}
                                                </span>
                                            </p>
                                            <a href="{% url 'factory:production_line_detail' line.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;">عرض التفاصيل</a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12 text-center py-4">
                                <p>لا توجد خطوط إنتاج حالياً</p>
                                <a href="{% url 'factory:production_line_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                                    <i class="fas fa-plus"></i> إضافة خط إنتاج جديد
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Production Orders -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background-color: var(--light-accent); color: var(--dark-text);">
                    <h5 class="mb-0"><i class="fas fa-tasks"></i> أوامر الإنتاج الحالية</h5>
                    <a href="{% url 'factory:production_order_list' %}" class="btn btn-sm" style="background-color: white; color: var(--light-accent);">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>خط الإنتاج</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء المتوقع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if production_orders %}
                                    {% for order in production_orders %}
                                        <tr>
                                            <td>{{ order.order.order_number }}</td>
                                            <td>{{ order.production_line.name }}</td>
                                            <td>{{ order.start_date|date:"Y-m-d" }}</td>
                                            <td>{{ order.estimated_completion|date:"Y-m-d" }}</td>
                                            <td>
                                                <span class="badge {% if order.status == 'completed' %}bg-success
                                                    {% elif order.status == 'in_progress' %}bg-primary
                                                    {% elif order.status == 'stalled' %}bg-warning
                                                    {% elif order.status == 'cancelled' %}bg-danger
                                                    {% else %}bg-secondary{% endif %}">
                                                    {{ order.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'factory:production_order_detail' order.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'factory:production_order_update' order.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد أوامر إنتاج حالية</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Statistics -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--secondary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> إحصائيات الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>إجمالي أوامر الإنتاج</h6>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 100%; background-color: var(--primary);" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">{{ production_orders.count }}</div>
                        </div>
                    </div>
                    {% for line in production_lines %}
                    <div class="mb-3">
                        <h6>{{ line.name }}</h6>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: {% if line.is_active %}85{% else %}30{% endif %}%; background-color: var(--primary);" aria-valuenow="{% if line.is_active %}85{% else %}30{% endif %}" aria-valuemin="0" aria-valuemax="100">{% if line.is_active %}نشط{% else %}غير نشط{% endif %}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--alert); color: white;">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> تنبيهات الإنتاج</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        {% for line in production_lines %}
                            {% if not line.is_active %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-tools text-warning me-2"></i>
                                        {{ line.name }} غير نشط
                                    </div>
                                    <span class="badge rounded-pill" style="background-color: var(--alert); color: white;">عاجل</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if stalled_orders %}
                            {% for order in stalled_orders %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                        أمر الإنتاج {{ order.order.order_number }} متعطل
                                    </div>
                                    <a href="{% url 'factory:production_order_detail' order.pk %}" class="badge rounded-pill bg-danger">عاجل</a>
                                </li>
                            {% endfor %}
                        {% endif %}
                        
                        {% if recent_issues %}
                            {% for issue in recent_issues %}
                                {% if not issue.resolved %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-exclamation-circle text-warning me-2"></i>
                                        مشكلة: {{ issue.title }}
                                    </div>
                                    <a href="{% url 'factory:production_issue_detail' issue.pk %}" class="badge rounded-pill {% if issue.severity == 'critical' %}bg-danger{% elif issue.severity == 'high' %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                        {{ issue.get_severity_display }}
                                    </a>
                                </li>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        
                        {% if not production_lines and not production_orders %}
                            <li class="list-group-item text-center">
                                لا توجد تنبيهات حالياً
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
