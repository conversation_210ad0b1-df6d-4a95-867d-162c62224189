{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "قائمة التركيبات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% trans "قائمة التركيبات" %}</h1>
        <a href="{% url 'installations:installation_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة تركيب جديد" %}
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">{% trans "تصفية النتائج" %}</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        {% for status_value, status_name in status_choices %}
                            <option value="{{ status_value }}" {% if current_status == status_value %}selected{% endif %}>
                                {{ status_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label">{% trans "بحث" %}</label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="{% trans 'رقم الطلب، ملاحظات...' %}"
                           value="{{ search_query }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Installations List -->
    {% if installations %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>{% trans "رقم" %}</th>
                        <th>{% trans "رقم الطلب" %}</th>
                        <th>{% trans "الحالة" %}</th>
                        <th>{% trans "موعد التركيب" %}</th>
                        <th>{% trans "فريق التركيب" %}</th>
                        <th>{% trans "تقييم الجودة" %}</th>
                        <th>{% trans "إجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for installation in installations %}
                        <tr>
                            <td>{{ installation.id }}</td>
                            <td>{{ installation.order.order_number }}</td>
                            <td>
                                {% if installation.status == 'pending' %}
                                    <span class="badge bg-warning">{{ installation.get_status_display }}</span>
                                {% elif installation.status == 'scheduled' %}
                                    <span class="badge bg-info">{{ installation.get_status_display }}</span>
                                {% elif installation.status == 'in_progress' %}
                                    <span class="badge bg-primary">{{ installation.get_status_display }}</span>
                                {% elif installation.status == 'completed' %}
                                    <span class="badge bg-success">{{ installation.get_status_display }}</span>
                                {% elif installation.status == 'cancelled' %}
                                    <span class="badge bg-danger">{{ installation.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if installation.scheduled_date %}
                                    {{ installation.scheduled_date|date:"Y-m-d" }}
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if installation.team %}
                                    {{ installation.team.name }}
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if installation.quality_rating %}
                                    <div class="star-rating">
                                        {% for i in "12345"|make_list %}
                                            {% if forloop.counter <= installation.quality_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <span class="text-muted">{% trans "غير مقيم" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'installations:installation_detail' installation.id %}"
                                       class="btn btn-sm btn-info" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'installations:installation_update' installation.id %}"
                                       class="btn btn-sm btn-warning" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'installations:installation_delete' installation.id %}"
                                       class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if current_status %}&status={{ current_status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if current_status %}&status={{ current_status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_status %}&status={{ current_status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> {% trans "لا توجد تركيبات متاحة." %}
        </div>
    {% endif %}
</div>
{% endblock %}
