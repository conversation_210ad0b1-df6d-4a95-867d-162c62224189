{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if form.instance.id %}
        {% trans "تعديل التركيب" %} #{{ form.instance.id }}
    {% else %}
        {% trans "إضافة تركيب جديد" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'installations:dashboard' %}">{% trans "التركيبات" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                {% if form.instance.id %}
                    {% trans "تعديل التركيب" %} #{{ form.instance.id }}
                {% else %}
                    {% trans "إضافة تركيب جديد" %}
                {% endif %}
            </li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                {% if form.instance.id %}
                    {% trans "تعديل التركيب" %} #{{ form.instance.id }}
                {% else %}
                    {% trans "إضافة تركيب جديد" %}
                {% endif %}
            </h4>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2">{% trans "معلومات أساسية" %}</h5>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.order.id_for_label }}" class="form-label">{{ form.order.label }}</label>
                            {% render_field form.order class="form-select" %}
                            {% if form.order.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.order.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.order.help_text %}
                                <small class="form-text text-muted">{{ form.order.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                            {% render_field form.customer class="form-select" %}
                            {% if form.customer.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.customer.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.customer.help_text %}
                                <small class="form-text text-muted">{{ form.customer.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.branch.id_for_label }}" class="form-label">{{ form.branch.label }}</label>
                            {% render_field form.branch class="form-select" %}
                            {% if form.branch.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.branch.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.branch.help_text %}
                                <small class="form-text text-muted">{{ form.branch.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.invoice_number.id_for_label }}" class="form-label">{{ form.invoice_number.label }}</label>
                            {% render_field form.invoice_number class="form-control" %}
                            {% if form.invoice_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.invoice_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.invoice_number.help_text %}
                                <small class="form-text text-muted">{{ form.invoice_number.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2">{% trans "حالة التركيب" %}</h5>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                            {% render_field form.status class="form-select" %}
                            {% if form.status.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.status.help_text %}
                                <small class="form-text text-muted">{{ form.status.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.scheduled_date.id_for_label }}" class="form-label">{{ form.scheduled_date.label }}</label>
                            {% render_field form.scheduled_date class="form-control" type="datetime-local" %}
                            {% if form.scheduled_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.scheduled_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.scheduled_date.help_text %}
                                <small class="form-text text-muted">{{ form.scheduled_date.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.technician.id_for_label }}" class="form-label">{{ form.technician.label }}</label>
                            {% render_field form.technician class="form-select" %}
                            {% if form.technician.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.technician.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.technician.help_text %}
                                <small class="form-text text-muted">{{ form.technician.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check mt-4">
                                {% render_field form.payment_verified class="form-check-input" %}
                                <label class="form-check-label" for="{{ form.payment_verified.id_for_label }}">
                                    {{ form.payment_verified.label }}
                                </label>
                                {% if form.payment_verified.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.payment_verified.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.payment_verified.help_text %}
                                    <small class="form-text text-muted">{{ form.payment_verified.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2">{% trans "ملاحظات" %}</h5>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                            {% render_field form.notes class="form-control" rows="4" %}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.notes.help_text %}
                                <small class="form-text text-muted">{{ form.notes.help_text }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12 d-flex justify-content-between">
                        <a href="{% url 'installations:dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "العودة" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if form.instance.id %}
                                {% trans "حفظ التعديلات" %}
                            {% else %}
                                {% trans "إضافة التركيب" %}
                            {% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-populate scheduled_date with current datetime if empty
        var scheduledDateField = document.getElementById('{{ form.scheduled_date.id_for_label }}');
        if (scheduledDateField && !scheduledDateField.value) {
            var now = new Date();
            var year = now.getFullYear();
            var month = String(now.getMonth() + 1).padStart(2, '0');
            var day = String(now.getDate()).padStart(2, '0');
            var hours = String(now.getHours()).padStart(2, '0');
            var minutes = String(now.getMinutes()).padStart(2, '0');
            
            scheduledDateField.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
        
        // Link customer selection to order selection
        var orderField = document.getElementById('{{ form.order.id_for_label }}');
        var customerField = document.getElementById('{{ form.customer.id_for_label }}');
        
        if (orderField && customerField) {
            orderField.addEventListener('change', function() {
                var selectedOption = orderField.options[orderField.selectedIndex];
                var customerInfo = selectedOption.getAttribute('data-customer-id');
                
                if (customerInfo) {
                    for (var i = 0; i < customerField.options.length; i++) {
                        if (customerField.options[i].value === customerInfo) {
                            customerField.selectedIndex = i;
                            break;
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}
