# Generated by Django 3.2.25 on 2025-05-04 08:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Installation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, verbose_name='رقم الفاتورة')),
                ('scheduled_date', models.DateTimeField(blank=True, null=True, verbose_name='موعد التركيب')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('scheduled', 'تم الجدولة'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('payment_verified', models.BooleanField(default=False, verbose_name='تم التحقق من السداد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installations', to='accounts.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_installations', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installations', to='customers.customer', verbose_name='العميل')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installations', to='orders.order', verbose_name='الطلب')),
                ('team_leader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lead_installations', to=settings.AUTH_USER_MODEL, verbose_name='قائد الفريق')),
                ('technician', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_installations', to=settings.AUTH_USER_MODEL, verbose_name='الفني المسؤول')),
            ],
            options={
                'verbose_name': 'تركيب',
                'verbose_name_plural': 'التركيبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TransportRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_location', models.TextField(verbose_name='من موقع')),
                ('to_location', models.TextField(verbose_name='إلى موقع')),
                ('scheduled_date', models.DateTimeField(blank=True, null=True, verbose_name='موعد النقل')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('scheduled', 'تم الجدولة'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_transports', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('driver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_transports', to=settings.AUTH_USER_MODEL, verbose_name='السائق المسؤول')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport_requests', to='installations.installation', verbose_name='طلب التركيب')),
                ('team_leader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lead_transports', to=settings.AUTH_USER_MODEL, verbose_name='قائد الفريق')),
            ],
            options={
                'verbose_name': 'طلب نقل',
                'verbose_name_plural': 'طلبات النقل',
                'ordering': ['-created_at'],
            },
        ),
    ]
