# Generated by Django 4.2.9 on 2025-05-06 23:18

from django.db import migrations

def set_default_category(apps, schema_editor):
    Product = apps.get_model('inventory', 'Product')
    Category = apps.get_model('inventory', 'Category')
    default_category = Category.objects.first()  # Using first category as default
    if default_category:
        Product.objects.filter(category__isnull=True).update(category=default_category)

class Migration(migrations.Migration):
    dependencies = [
        ('inventory', '0003_alter_stocktransaction_reason_and_more'),
    ]

    operations = [
        migrations.RunPython(set_default_category),
    ]
