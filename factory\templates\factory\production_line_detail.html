{% extends 'base.html' %}

{% block title %}{{ production_line.name }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">{{ production_line.name }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_line_list' %}">خطوط الإنتاج</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ production_line.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <a href="{% url 'factory:production_line_update' production_line.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{% url 'factory:production_line_delete' production_line.pk %}" class="btn" style="background-color: var(--alert); color: white;">
                    <i class="fas fa-trash"></i> حذف
                </a>
            </div>
        </div>
    </div>

    <!-- Production Line Details -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> تفاصيل خط الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%;">الاسم</th>
                                    <td>{{ production_line.name }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <span class="badge {% if production_line.is_active %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                            {% if production_line.is_active %}نشط{% else %}غير نشط{% endif %}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100" style="border-color: var(--neutral);">
                                <div class="card-header" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <h6 class="mb-0">الوصف</h6>
                                </div>
                                <div class="card-body">
                                    {% if production_line.description %}
                                        <p>{{ production_line.description }}</p>
                                    {% else %}
                                        <p class="text-muted">لا يوجد وصف</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Orders -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background-color: var(--light-accent); color: var(--dark-text);">
                    <h5 class="mb-0"><i class="fas fa-tasks"></i> أوامر الإنتاج</h5>
                    <a href="{% url 'factory:production_order_create' %}" class="btn btn-sm" style="background-color: var(--primary); color: white;">
                        <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء المتوقع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if production_orders %}
                                    {% for order in production_orders %}
                                        <tr>
                                            <td>{{ order.order.order_number }}</td>
                                            <td>{{ order.start_date|date:"Y-m-d"|default:"-" }}</td>
                                            <td>{{ order.estimated_completion|date:"Y-m-d"|default:"-" }}</td>
                                            <td>
                                                <span class="badge 
                                                    {% if order.status == 'completed' %}bg-success
                                                    {% elif order.status == 'in_progress' %}" style="background-color: var(--primary); color: white;
                                                    {% elif order.status == 'quality_check' %}" style="background-color: var(--light-accent); color: var(--dark-text);
                                                    {% elif order.status == 'cancelled' %}" style="background-color: var(--alert); color: white;
                                                    {% else %}bg-secondary{% endif %}">
                                                    {{ order.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'factory:production_order_detail' order.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'factory:production_order_update' order.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'factory:production_order_delete' order.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">لا توجد أوامر إنتاج لهذا الخط</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Line Statistics -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--secondary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> إحصائيات خط الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-tasks fa-3x mb-3" style="color: var(--primary);"></i>
                                    <h5 class="card-title">إجمالي أوامر الإنتاج</h5>
                                    <p class="card-text display-6">{{ production_orders.count }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-spinner fa-3x mb-3" style="color: var(--primary);"></i>
                                    <h5 class="card-title">قيد التنفيذ</h5>
                                    <p class="card-text display-6">{{ production_orders.in_progress_count|default:"0" }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-check-circle fa-3x mb-3" style="color: var(--success);"></i>
                                    <h5 class="card-title">مكتملة</h5>
                                    <p class="card-text display-6">{{ production_orders.completed_count|default:"0" }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3" style="color: var(--alert);"></i>
                                    <h5 class="card-title">ملغاة</h5>
                                    <p class="card-text display-6">{{ production_orders.cancelled_count|default:"0" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
