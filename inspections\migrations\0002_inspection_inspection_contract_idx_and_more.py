# Generated by Django 5.2 on 2025-05-05 07:25

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('customers', '0002_auto_20250505_1003'),
        ('inspections', '0001_initial'),
        ('orders', '0002_auto_20250505_1003'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['contract_number'], name='inspection_contract_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['customer'], name='inspection_customer_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['branch'], name='inspection_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['inspector'], name='inspection_inspector_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['status'], name='inspection_status_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['result'], name='inspection_result_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['request_date'], name='inspection_req_date_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['scheduled_date'], name='inspection_sched_date_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['order'], name='inspection_order_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['created_at'], name='inspection_created_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['inspection'], name='inspection_eval_insp_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['criteria'], name='inspection_eval_criteria_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['rating'], name='inspection_eval_rating_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['created_by'], name='inspection_eval_creator_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['created_at'], name='inspection_eval_created_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['inspection'], name='inspection_notif_insp_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['type'], name='inspection_notif_type_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['is_read'], name='inspection_notif_read_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['created_at'], name='inspection_notif_created_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['scheduled_for'], name='inspection_notif_scheduled_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['report_type'], name='inspection_report_type_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['branch'], name='inspection_report_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['date_from'], name='inspection_report_from_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['date_to'], name='inspection_report_to_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['created_at'], name='inspection_report_created_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['created_by'], name='inspection_report_creator_idx'),
        ),
    ]
