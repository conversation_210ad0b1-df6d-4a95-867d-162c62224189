{% extends 'base.html' %}

{% block title %}أمر الإنتاج: {{ production_order.order.order_number }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">أمر الإنتاج: {{ production_order.order.order_number }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_list' %}">أوامر الإنتاج</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ production_order.order.order_number }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <a href="{% url 'factory:production_order_update' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{% url 'factory:production_order_delete' production_order.pk %}" class="btn" style="background-color: var(--alert); color: white;">
                    <i class="fas fa-trash"></i> حذف
                </a>
            </div>
        </div>
    </div>

    <!-- Production Order Details -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> تفاصيل أمر الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%;">رقم الطلب</th>
                                    <td>{{ production_order.order.order_number }}</td>
                                </tr>
                                <tr>
                                    <th>خط الإنتاج</th>
                                    <td>
                                        <a href="{% url 'factory:production_line_detail' production_order.production_line.pk %}">
                                            {{ production_order.production_line.name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <span class="badge {% if production_order.status == 'completed' %}bg-success
                                            {% elif production_order.status == 'in_progress' %}bg-primary
                                            {% elif production_order.status == 'stalled' %}bg-warning
                                            {% elif production_order.status == 'cancelled' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ production_order.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ البدء</th>
                                    <td>{{ production_order.start_date|date:"Y-m-d H:i"|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ الانتهاء</th>
                                    <td>{{ production_order.end_date|date:"Y-m-d H:i"|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <th>التاريخ المتوقع للانتهاء</th>
                                    <td>{{ production_order.estimated_completion|date:"Y-m-d H:i"|default:"-" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100" style="border-color: var(--neutral);">
                                <div class="card-header" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <h6 class="mb-0">معلومات الطلب</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%;">العميل</th>
                                            <td>{{ production_order.order.customer.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الطلب</th>
                                            <td>{{ production_order.order.order_date|date:"Y-m-d"|default:"-" }}</td>
                                        </tr>
                                        <tr>
                                            <th>حالة الطلب</th>
                                            <td>
                                                <span class="badge {% if production_order.order.status == 'completed' %}bg-success
                                                    {% elif production_order.order.status == 'processing' %}bg-info
                                                    {% elif production_order.order.status == 'cancelled' %}bg-danger
                                                    {% else %}bg-secondary{% endif %}">
                                                    {{ production_order.order.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                    
                                    <div class="mt-3">
                                        <h6>ملاحظات</h6>
                                        <p>{{ production_order.notes|default:"لا توجد ملاحظات" }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Stages -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background-color: var(--secondary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-list-ol"></i> مراحل الإنتاج</h5>
                    <a href="{% url 'factory:production_stage_create' production_order.pk %}" class="btn btn-sm" style="background-color: white; color: var(--secondary);">
                        <i class="fas fa-plus"></i> إضافة مرحلة جديدة
                    </a>
                </div>
                <div class="card-body">
                    {% if stages %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المرحلة</th>
                                        <th>تاريخ البدء</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الحالة</th>
                                        <th>المسؤول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stage in stages %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>{{ stage.name }}</td>
                                            <td>{{ stage.start_date|date:"Y-m-d"|default:"-" }}</td>
                                            <td>{{ stage.end_date|date:"Y-m-d"|default:"-" }}</td>
                                            <td>
                                                <span class="badge {% if stage.completed %}bg-success{% else %}bg-secondary{% endif %}">
                                                    {% if stage.completed %}مكتملة{% else %}قيد التنفيذ{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ stage.assigned_to.get_full_name|default:stage.assigned_to.username|default:"-" }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'factory:production_stage_update' stage.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'factory:production_stage_delete' stage.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p>لا توجد مراحل إنتاج مسجلة لهذا الأمر</p>
                            <a href="{% url 'factory:production_stage_create' production_order.pk %}" class="btn" style="background-color: var(--secondary); color: white;">
                                <i class="fas fa-plus"></i> إضافة مرحلة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Production Issues -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background-color: var(--alert); color: white;">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> مشاكل الإنتاج</h5>
                    <a href="{% url 'factory:production_issue_create' production_order.pk %}" class="btn btn-sm" style="background-color: white; color: var(--alert);">
                        <i class="fas fa-plus"></i> تسجيل مشكلة جديدة
                    </a>
                </div>
                <div class="card-body">
                    {% if issues %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>العنوان</th>
                                        <th>الخطورة</th>
                                        <th>تاريخ الإبلاغ</th>
                                        <th>الحالة</th>
                                        <th>تم الإبلاغ بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for issue in issues %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>{{ issue.title }}</td>
                                            <td>
                                                {% if issue.severity == 'critical' %}
                                                    <span class="badge bg-danger">حرجة</span>
                                                {% elif issue.severity == 'high' %}
                                                    <span class="badge bg-warning text-dark">عالية</span>
                                                {% elif issue.severity == 'medium' %}
                                                    <span class="badge bg-info text-dark">متوسطة</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">منخفضة</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ issue.reported_at|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                {% if issue.resolved %}
                                                    <span class="badge bg-success">تم الحل</span>
                                                {% else %}
                                                    <span class="badge bg-danger">غير محلولة</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ issue.reported_by.get_full_name|default:issue.reported_by.username|default:"-" }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'factory:production_issue_detail' issue.pk %}" class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'factory:production_issue_update' issue.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p>لا توجد مشاكل إنتاج مسجلة لهذا الأمر</p>
                            <a href="{% url 'factory:production_issue_create' production_order.pk %}" class="btn" style="background-color: var(--alert); color: white;">
                                <i class="fas fa-plus"></i> تسجيل مشكلة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
