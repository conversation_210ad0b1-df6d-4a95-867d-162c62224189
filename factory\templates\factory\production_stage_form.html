{% extends 'base.html' %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">{{ title }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_list' %}">أوامر الإنتاج</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_detail' production_order.pk %}">{{ production_order.order.order_number }}</a></li>
                    {% if stage %}
                        <li class="breadcrumb-item active" aria-current="page">تعديل مرحلة الإنتاج</li>
                    {% else %}
                        <li class="breadcrumb-item active" aria-current="page">إضافة مرحلة إنتاج جديدة</li>
                    {% endif %}
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى أمر الإنتاج
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--secondary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> {{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {{ form.production_order }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.start_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-check mb-3">
                                    {{ form.completed }}
                                    <label for="{{ form.completed.id_for_label }}" class="form-check-label">{{ form.completed.label }}</label>
                                    {% if form.completed.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.completed.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.assigned_to.id_for_label }}" class="form-label">{{ form.assigned_to.label }}</label>
                                    {{ form.assigned_to }}
                                    {% if form.assigned_to.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.assigned_to.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.description.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="submit" class="btn" style="background-color: var(--secondary); color: white;">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
