# Generated by Django 4.2.21 on 2025-06-12 11:05

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0009_alter_product_currency'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='stocktransaction',
            options={'ordering': ['transaction_date', 'id'], 'verbose_name': 'حركة مخزون', 'verbose_name_plural': 'حركات المخزون'},
        ),
        migrations.RemoveIndex(
            model_name='stocktransaction',
            name='transaction_date_idx',
        ),
        migrations.AddField(
            model_name='stocktransaction',
            name='running_balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الجاري'),
        ),
        migrations.AddField(
            model_name='stocktransaction',
            name='transaction_date',
            field=models.DateTimeField(default=datetime.datetime.now, verbose_name='تاريخ الحركة'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_date'], name='transaction_date_idx'),
        ),
    ]
