# Generated by Django 5.2 on 2025-05-05 09:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_auto_20250505_1003'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='stocktransaction',
            name='reason',
            field=models.CharField(choices=[('purchase', 'شراء'), ('sale', 'بيع'), ('return', 'مرتجع'), ('transfer', 'نقل'), ('inventory_check', 'جرد'), ('damage', 'تلف'), ('production', 'إنتاج'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='السبب'),
        ),
        migrations.AlterModelOptions(
            name='purchaseorderitem',
            options={'ordering': ['purchase_order', 'product'], 'verbose_name': 'عنصر طلب الشراء', 'verbose_name_plural': 'عناصر طلب الشراء'},
        ),
        migrations.AlterModelOptions(
            name='warehouse',
            options={'ordering': ['name'], 'verbose_name': 'مستودع', 'verbose_name_plural': 'المستودعات'},
        ),
        migrations.AlterModelOptions(
            name='warehouselocation',
            options={'ordering': ['warehouse', 'name'], 'verbose_name': 'موقع مستودع', 'verbose_name_plural': 'مواقع المستودعات'},
        ),
        migrations.RemoveIndex(
            model_name='inventoryadjustment',
            name='inv_adj_batch_idx',
        ),
        migrations.RemoveIndex(
            model_name='inventoryadjustment',
            name='inv_adj_creator_idx',
        ),
        migrations.RemoveIndex(
            model_name='productbatch',
            name='product_batch_location_idx',
        ),
        migrations.RemoveIndex(
            model_name='productbatch',
            name='product_batch_barcode_idx',
        ),
        migrations.RemoveIndex(
            model_name='productbatch',
            name='product_batch_created_idx',
        ),
        migrations.RemoveIndex(
            model_name='purchaseorder',
            name='purchase_order_warehouse_idx',
        ),
        migrations.RemoveIndex(
            model_name='purchaseorder',
            name='purchase_order_expected_idx',
        ),
        migrations.RemoveIndex(
            model_name='purchaseorderitem',
            name='po_item_order_idx',
        ),
        migrations.RemoveIndex(
            model_name='purchaseorderitem',
            name='po_item_product_idx',
        ),
        migrations.RemoveIndex(
            model_name='purchaseorderitem',
            name='po_item_received_idx',
        ),
        migrations.RemoveIndex(
            model_name='stocktransaction',
            name='stock_trans_creator_idx',
        ),
        migrations.RemoveIndex(
            model_name='stocktransaction',
            name='stock_trans_prod_type_idx',
        ),
        migrations.RemoveIndex(
            model_name='warehouse',
            name='warehouse_name_idx',
        ),
        migrations.RemoveIndex(
            model_name='warehouse',
            name='warehouse_code_idx',
        ),
        migrations.RemoveIndex(
            model_name='warehouse',
            name='warehouse_branch_idx',
        ),
        migrations.RemoveIndex(
            model_name='warehouse',
            name='warehouse_active_idx',
        ),
        migrations.RemoveIndex(
            model_name='warehouse',
            name='warehouse_manager_idx',
        ),
        migrations.RenameIndex(
            model_name='inventoryadjustment',
            new_name='adjustment_product_idx',
            old_name='inv_adj_product_idx',
        ),
        migrations.RenameIndex(
            model_name='inventoryadjustment',
            new_name='adjustment_type_idx',
            old_name='inv_adj_type_idx',
        ),
        migrations.RenameIndex(
            model_name='inventoryadjustment',
            new_name='adjustment_date_idx',
            old_name='inv_adj_date_idx',
        ),
        migrations.RenameIndex(
            model_name='productbatch',
            new_name='batch_product_idx',
            old_name='product_batch_product_idx',
        ),
        migrations.RenameIndex(
            model_name='productbatch',
            new_name='batch_expiry_idx',
            old_name='product_batch_expiry_idx',
        ),
        migrations.RenameIndex(
            model_name='purchaseorder',
            new_name='po_supplier_idx',
            old_name='purchase_order_supplier_idx',
        ),
        migrations.RenameIndex(
            model_name='purchaseorder',
            new_name='po_status_idx',
            old_name='purchase_order_status_idx',
        ),
        migrations.RenameIndex(
            model_name='purchaseorder',
            new_name='po_date_idx',
            old_name='purchase_order_date_idx',
        ),
        migrations.RenameIndex(
            model_name='stockalert',
            new_name='alert_product_idx',
            old_name='stock_alert_product_idx',
        ),
        migrations.RenameIndex(
            model_name='stockalert',
            new_name='alert_type_idx',
            old_name='stock_alert_type_idx',
        ),
        migrations.RenameIndex(
            model_name='stockalert',
            new_name='alert_status_idx',
            old_name='stock_alert_status_idx',
        ),
        migrations.RenameIndex(
            model_name='stockalert',
            new_name='alert_created_at_idx',
            old_name='stock_alert_created_idx',
        ),
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='transaction_product_idx',
            old_name='stock_trans_product_idx',
        ),
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='transaction_type_idx',
            old_name='stock_trans_type_idx',
        ),
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='transaction_date_idx',
            old_name='stock_trans_date_idx',
        ),
        migrations.AlterUniqueTogether(
            name='productbatch',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='warehouse',
            name='created_at',
        ),
        migrations.AlterField(
            model_name='inventoryadjustment',
            name='adjustment_type',
            field=models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص')], max_length=10, verbose_name='نوع التسوية'),
        ),
        migrations.AlterField(
            model_name='inventoryadjustment',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة'),
        ),
        migrations.AlterField(
            model_name='inventoryadjustment',
            name='quantity_after',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية بعد'),
        ),
        migrations.AlterField(
            model_name='inventoryadjustment',
            name='quantity_before',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية قبل'),
        ),
        migrations.AlterField(
            model_name='productbatch',
            name='cost_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='سعر التكلفة'),
        ),
        migrations.AlterField(
            model_name='productbatch',
            name='expiry_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الصلاحية'),
        ),
        migrations.AlterField(
            model_name='productbatch',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_batches', to='inventory.warehouselocation', verbose_name='الموقع'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='expected_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='order_date',
            field=models.DateField(auto_now_add=True, verbose_name='تاريخ الطلب'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('partial', 'استلام جزئي'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبلغ'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='warehouse',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders', to='inventory.warehouse', verbose_name='المستودع'),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='alert_type',
            field=models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('expiry', 'قرب انتهاء الصلاحية'), ('out_of_stock', 'نفاد المخزون')], max_length=15, verbose_name='نوع التنبيه'),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='resolved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة'),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_stock_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تمت المعالجة بواسطة'),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='status',
            field=models.CharField(choices=[('active', 'نشط'), ('resolved', 'تمت المعالجة'), ('ignored', 'تم تجاهله')], default='active', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_transactions', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='inventory.product', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='transaction_type',
            field=models.CharField(choices=[('in', 'وارد'), ('out', 'صادر'), ('transfer', 'نقل'), ('adjustment', 'تسوية')], max_length=10, verbose_name='نوع الحركة'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='address',
            field=models.TextField(blank=True, verbose_name='العنوان'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='contact_person',
            field=models.CharField(blank=True, max_length=100, verbose_name='جهة الاتصال'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='phone',
            field=models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='رمز المستودع'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='المدير'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم المستودع'),
        ),
        migrations.AlterField(
            model_name='warehouselocation',
            name='code',
            field=models.CharField(max_length=30, verbose_name='رمز الموقع'),
        ),
        migrations.AlterField(
            model_name='warehouselocation',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='inventory.warehouse', verbose_name='المستودع'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['batch_number'], name='batch_number_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['order_number'], name='po_number_idx'),
        ),
        migrations.DeleteModel(
            name='StockTransactionReason',
        ),
        migrations.RemoveField(
            model_name='productbatch',
            name='notes',
        ),
    ]
