{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accounts:form_field_list' %}">إدارة حقول النماذج</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header bg-danger text-white">
            <h4 class="mb-0">{{ title }}</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> تحذير: هذا الإجراء لا يمكن التراجع عنه.
            </div>
            
            <p class="lead">هل أنت متأكد من رغبتك في حذف الحقل التالي؟</p>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الحقل</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">نوع النموذج</th>
                            <td>{{ form_field.get_form_type_display }}</td>
                        </tr>
                        <tr>
                            <th>اسم الحقل</th>
                            <td>{{ form_field.field_name }}</td>
                        </tr>
                        <tr>
                            <th>عنوان الحقل</th>
                            <td>{{ form_field.field_label }}</td>
                        </tr>
                        <tr>
                            <th>نوع الحقل</th>
                            <td>{{ form_field.get_field_type_display }}</td>
                        </tr>
                        <tr>
                            <th>مطلوب</th>
                            <td>
                                {% if form_field.required %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>مفعل</th>
                            <td>
                                {% if form_field.enabled %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="d-flex justify-content-center">
                    <button type="submit" class="btn btn-danger mx-2">
                        <i class="fas fa-trash"></i> نعم، حذف الحقل
                    </button>
                    <a href="{% url 'accounts:form_field_list' %}" class="btn btn-secondary mx-2">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
