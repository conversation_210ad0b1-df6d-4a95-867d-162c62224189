{% extends 'base.html' %}

{% block title %}تسجيل الدخول - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% if error_message %}
                    <div class="alert alert-danger alert-dismissible fade show">
                        {{ error_message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">اسم المستخدم</label>
                            {% if form.username %}
                                {{ form.username }}
                            {% else %}
                                <input type="text" name="username" class="form-control" placeholder="اسم المستخدم" required>
                            {% endif %}
                            {% if form.username.errors %}
                            <div class="text-danger">
                                {% for error in form.username.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">كلمة المرور</label>
                            {% if form.password %}
                                {{ form.password }}
                            {% else %}
                                <input type="password" name="password" class="form-control" placeholder="كلمة المرور" required>
                            {% endif %}
                            {% if form.password.errors %}
                            <div class="text-danger">
                                {% for error in form.password.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        إذا واجهت مشكلة في تسجيل الدخول، يرجى التواصل مع مسؤول النظام.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
