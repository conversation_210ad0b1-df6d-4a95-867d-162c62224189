# Generated by Django 4.2.21 on 2025-05-27 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspections', '0002_inspection_inspection_contract_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='inspection',
            name='google_drive_file_id',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='معرف ملف Google Drive'),
        ),
        migrations.AddField(
            model_name='inspection',
            name='google_drive_file_name',
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name='اسم الملف في Google Drive'),
        ),
        migrations.AddField(
            model_name='inspection',
            name='google_drive_file_url',
            field=models.URLField(blank=True, null=True, verbose_name='رابط ملف Google Drive'),
        ),
        migrations.AddField(
            model_name='inspection',
            name='is_uploaded_to_drive',
            field=models.BooleanField(default=False, verbose_name='تم الرفع إلى Google Drive'),
        ),
    ]
