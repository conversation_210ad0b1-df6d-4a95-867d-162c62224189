{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}الموردين{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item active" aria-current="page">الموردين</li>
{% endblock %}

{% block quick_actions %}
<button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
    <i class="fas fa-plus"></i> إضافة مورد
</button>
<a href="{% url 'inventory:purchase_order_list' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-shopping-cart"></i> طلبات الشراء
</a>
{% endblock %}

{% block inventory_content %}
<div class="supplier-list-container">
    <!-- إحصائيات سريعة -->
    <div class="stats-cards-container">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">إجمالي الموردين</div>
                <div class="stat-card-value">{{ suppliers|length }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 5% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">طلبات شراء نشطة</div>
                <div class="stat-card-value">{{ active_purchase_orders }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 8% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">إجمالي المشتريات</div>
                <div class="stat-card-value">{{ total_purchases }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 12% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">أفضل المنتجات</div>
                <div class="stat-card-value">{{ top_products_count }}</div>
                <div class="stat-card-change neutral">
                    <i class="fas fa-minus"></i> لا تغيير
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filters-container mb-4">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم المورد، جهة الاتصال، الهاتف...">
                    </div>
                    <div class="col-md-4">
                        <label for="sort" class="form-label">ترتيب</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم (أ-ي)</option>
                            <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>الاسم (ي-أ)</option>
                            <option value="contact_person" {% if sort_by == 'contact_person' %}selected{% endif %}>جهة الاتصال</option>
                            <option value="phone" {% if sort_by == 'phone' %}selected{% endif %}>رقم الهاتف</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:supplier_list' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- قائمة الموردين -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                الموردين
                {% if suppliers %}
                <span class="badge bg-primary">{{ suppliers|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Excel</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf"></i> PDF</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-print"></i> طباعة</a></li>
                    </ul>
                </div>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                    <i class="fas fa-plus"></i> إضافة مورد
                </button>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم المورد</th>
                            <th>جهة الاتصال</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>الرقم الضريبي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>
                                <strong>{{ supplier.name }}</strong>
                            </td>
                            <td>{{ supplier.contact_person|default:"-" }}</td>
                            <td>{{ supplier.phone|default:"-" }}</td>
                            <td>{{ supplier.email|default:"-" }}</td>
                            <td>{{ supplier.address|default:"-" }}</td>
                            <td>{{ supplier.tax_number|default:"-" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:supplier_update' supplier.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:supplier_delete' supplier.id %}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="{% url 'inventory:supplier_detail' supplier.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:purchase_order_create' %}?supplier={{ supplier.id }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">لا يوجد موردين مطابقين للبحث</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- الصفحات -->
        {% if page_obj.has_other_pages %}
        <div class="data-table-footer">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة مورد جديد -->
<div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSupplierModalLabel">إضافة مورد جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:supplier_create' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المورد</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_person" class="form-label">جهة الاتصال</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="1"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
