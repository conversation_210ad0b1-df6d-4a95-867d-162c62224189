/* Enhanced Forms Styles - تحسينات النماذج */

/* Form Container */
.form-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin: 20px 0;
}

/* Enhanced Input Fields */
.form-control-enhanced {
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.form-control-enhanced:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(52, 144, 220, 0.1);
    transform: translateY(-2px);
}

/* Floating Labels */
.form-floating-enhanced {
    position: relative;
    margin-bottom: 25px;
}

.form-floating-enhanced .form-control {
    padding: 20px 16px 6px 16px;
    height: auto;
}

.form-floating-enhanced label {
    position: absolute;
    top: 0;
    left: 16px;
    height: 100%;
    padding: 20px 0 0 0;
    pointer-events: none;
    border: none;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    color: #6c757d;
    font-weight: 500;
}

/* Enhanced Buttons */
.btn-enhanced {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    box-shadow: 0 4px 15px rgba(52, 144, 220, 0.3);
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 144, 220, 0.4);
    color: white;
}

/* Card Enhancements */
.card-enhanced {
    border: none;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.card-header-enhanced {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border: none;
    padding: 20px 30px;
    font-weight: 600;
}

/* Progressive Form Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.progress-step {
    background: white;
    border: 3px solid #e9ecef;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.progress-step.active {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
    transform: scale(1.1);
}

.progress-step.completed {
    border-color: var(--success);
    background: var(--success);
    color: white;
}

.progress-step.completed::after {
    content: '✓';
    font-size: 16px;
}

/* Enhanced Tabs */
.nav-tabs-enhanced {
    border: none;
    margin-bottom: 0;
}

.nav-tabs-enhanced .nav-link {
    border: none;
    border-radius: 15px 15px 0 0;
    padding: 15px 25px;
    color: #6c757d;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 5px;
    background: #f8f9fa;
}

.nav-tabs-enhanced .nav-link.active {
    background: white;
    color: var(--primary);
    transform: translateY(-3px);
    box-shadow: 0 -3px 10px rgba(0,0,0,0.1);
}

.nav-tabs-enhanced .nav-link:hover:not(.active) {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* File Upload Enhancement */
.file-upload-enhanced {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload-enhanced input[type=file] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: rgba(52, 144, 220, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: rgba(52, 144, 220, 0.1);
    transform: scale(1.02);
}

/* Form Validation Styles */
.form-control.is-valid {
    border-color: var(--success);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.8.8 3.5-3.5-.8-.8L2.3 5.73z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: var(--error);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Loading States */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .form-enhanced {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        color: white;
    }
    
    .form-control-enhanced {
        background: #2d3748;
        color: white;
        border-color: #4a5568;
    }
    
    .card-enhanced {
        background: #2d3748;
        color: white;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .form-enhanced {
        padding: 20px 15px;
        margin: 10px 0;
    }
    
    .progress-steps {
        flex-direction: column;
        align-items: center;
    }
    
    .progress-steps::before {
        display: none;
    }
    
    .progress-step {
        margin-bottom: 20px;
    }
    
    .nav-tabs-enhanced {
        flex-direction: column;
    }
    
    .nav-tabs-enhanced .nav-link {
        margin-bottom: 5px;
        margin-right: 0;
        border-radius: 10px;
    }
}
