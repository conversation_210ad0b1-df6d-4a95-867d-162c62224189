{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تأكيد حذف النسخة الاحتياطية" %}{% endblock %}

{% block extra_css %}
<style>
    .confirm-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    .confirm-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
        text-align: center;
    }
    .confirm-header i {
        font-size: 48px;
        color: #dc3545;
        margin-bottom: 20px;
    }
    .confirm-content {
        margin-bottom: 30px;
    }
    .confirm-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;
    }
    .backup-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="confirm-container">
        <div class="confirm-header">
            <i class="fas fa-trash-alt"></i>
            <h1>{% trans "تأكيد حذف النسخة الاحتياطية" %}</h1>
        </div>
        
        <div class="confirm-content">
            <div class="alert alert-danger">
                <p>{% trans "هل أنت متأكد من أنك تريد حذف هذه النسخة الاحتياطية؟" %}</p>
                <p><strong>{% trans "تحذير:" %}</strong> {% trans "هذه العملية لا يمكن التراجع عنها!" %}</p>
            </div>
            
            <div class="backup-info">
                <h5>{% trans "معلومات النسخة الاحتياطية:" %}</h5>
                <p><strong>{% trans "الاسم:" %}</strong> {{ backup.name }}</p>
                <p><strong>{% trans "قاعدة البيانات:" %}</strong> {{ backup.database_config.name }}</p>
                <p><strong>{% trans "النوع:" %}</strong> 
                    {% if backup.backup_type == 'full' %}
                        {% trans "نسخة كاملة" %}
                    {% else %}
                        {% trans "نسخة جزئية" %}
                    {% endif %}
                </p>
                <p><strong>{% trans "تاريخ الإنشاء:" %}</strong> {{ backup.created_at|date:"Y-m-d H:i" }}</p>
                <p><strong>{% trans "الحجم:" %}</strong> {{ backup.size|filesizeformat }}</p>
            </div>
        </div>
        
        <div class="confirm-footer">
            <form method="post">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger btn-lg">{% trans "نعم، حذف النسخة الاحتياطية" %}</button>
                <a href="{% url 'db_manager:backup_list' %}" class="btn btn-secondary btn-lg">{% trans "إلغاء" %}</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
