{% extends 'base.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if report %}
        {% trans 'تعديل التقرير' %}
    {% else %}
        {% trans 'إنشاء تقرير جديد' %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        {% if report %}
                            {% trans 'تعديل التقرير' %}
                        {% else %}
                            {% trans 'إنشاء تقرير جديد' %}
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="form-group mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">{% trans 'عنوان التقرير' %}</label>
            {% render_field form.title class="form-control" placeholder="أدخل عنوان التقرير" %}
                            {% if form.title.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.report_type.id_for_label }}" class="form-label">{% trans 'نوع التقرير' %}</label>
                            {% render_field form.report_type class="form-select" %}
                            {% if form.report_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.report_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{% trans 'وصف التقرير' %}</label>
            {% render_field form.description class="form-control" rows="4" placeholder="أدخل وصف التقرير" %}
                            {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.parameters.id_for_label }}" class="form-label">{% trans 'معلمات التقرير' %}</label>
            {% render_field form.parameters class="form-control" rows="4" placeholder="أدخل معلمات التقرير بتنسيق JSON" %}
                            <small class="form-text text-muted">
                                {% trans 'مثال: {"date_range": 30, "include_details": true}' %}
                            </small>
                            {% if form.parameters.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.parameters.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'reports:report_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans 'إلغاء' %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                {% if report %}
                                    {% trans 'حفظ التغييرات' %}
                                {% else %}
                                    {% trans 'إنشاء التقرير' %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Format JSON input on blur
        $('#{{ form.parameters.id_for_label }}').on('blur', function() {
            try {
                let value = $(this).val();
                if (value) {
                    let jsonObj = JSON.parse(value);
                    $(this).val(JSON.stringify(jsonObj, null, 2));
                }
            } catch (e) {
                // Invalid JSON - leave as is
            }
        });
    });
</script>
{% endblock %}
