# Generated by Django 4.2.9 on 2025-05-07 03:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0005_shippingdetails'),
        ('inspections', '0002_inspection_inspection_contract_idx_and_more'),
        ('accounts', '0002_role_userrole'),
        ('installations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InstallationIssue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المشكلة')),
                ('description', models.TextField(verbose_name='وصف المشكلة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('in_progress', 'جاري المعالجة'), ('resolved', 'تم الحل'), ('closed', 'مغلقة')], default='open', max_length=20, verbose_name='الحالة')),
                ('resolution', models.TextField(blank=True, verbose_name='الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مشكلة تركيب',
                'verbose_name_plural': 'مشاكل التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('scheduled', 'موعد تركيب'), ('status_change', 'تغيير الحالة'), ('quality_check', 'فحص الجودة'), ('issue', 'مشكلة')], max_length=20, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الإشعار')),
                ('message', models.TextField(verbose_name='نص الإشعار')),
                ('is_read', models.BooleanField(default=False, verbose_name='تمت القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'إشعار تركيب',
                'verbose_name_plural': 'إشعارات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationQualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('criteria', models.CharField(choices=[('alignment', 'المحاذاة'), ('finishing', 'التشطيب'), ('functionality', 'الوظائف'), ('safety', 'السلامة'), ('cleanliness', 'النظافة')], max_length=20, verbose_name='معيار التقييم')),
                ('rating', models.IntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='التقييم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='installations/quality/', verbose_name='صورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الفحص')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الخطوة')),
                ('description', models.TextField(blank=True, verbose_name='وصف الخطوة')),
                ('order', models.PositiveIntegerField(verbose_name='الترتيب')),
                ('is_completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='installations/steps/', verbose_name='صورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'خطوة تركيب',
                'verbose_name_plural': 'خطوات التركيب',
                'ordering': ['installation', 'order'],
            },
        ),
        migrations.CreateModel(
            name='InstallationTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفريق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فريق تركيب',
                'verbose_name_plural': 'فرق التركيب',
                'ordering': ['name'],
            },
        ),
        migrations.RemoveField(
            model_name='transportrequest',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='transportrequest',
            name='driver',
        ),
        migrations.RemoveField(
            model_name='transportrequest',
            name='installation',
        ),
        migrations.RemoveField(
            model_name='transportrequest',
            name='team_leader',
        ),
        migrations.AlterModelOptions(
            name='installation',
            options={'ordering': ['-scheduled_date'], 'verbose_name': 'عملية تركيب', 'verbose_name_plural': 'عمليات التركيب'},
        ),
        migrations.RemoveField(
            model_name='installation',
            name='branch',
        ),
        migrations.RemoveField(
            model_name='installation',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='installation',
            name='invoice_number',
        ),
        migrations.RemoveField(
            model_name='installation',
            name='payment_verified',
        ),
        migrations.RemoveField(
            model_name='installation',
            name='team_leader',
        ),
        migrations.RemoveField(
            model_name='installation',
            name='technician',
        ),
        migrations.AddField(
            model_name='installation',
            name='actual_end_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء التركيب الفعلي'),
        ),
        migrations.AddField(
            model_name='installation',
            name='actual_start_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء التركيب الفعلي'),
        ),
        migrations.AddField(
            model_name='installation',
            name='inspection',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='installations', to='inspections.inspection', verbose_name='المعاينة'),
        ),
        migrations.AddField(
            model_name='installation',
            name='quality_rating',
            field=models.IntegerField(blank=True, choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], null=True, verbose_name='تقييم الجودة'),
        ),
        migrations.AlterField(
            model_name='installation',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installations', to='orders.order', verbose_name='الطلب'),
        ),
        migrations.AlterField(
            model_name='installation',
            name='scheduled_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التركيب المجدول'),
        ),
        migrations.AlterField(
            model_name='installation',
            name='status',
            field=models.CharField(choices=[('pending', 'قيد الانتظار'), ('scheduled', 'مجدول'), ('in_progress', 'جاري التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['order'], name='install_order_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['inspection'], name='install_inspection_idx'),
        ),
        migrations.DeleteModel(
            name='TransportRequest',
        ),
        migrations.AddField(
            model_name='installationteam',
            name='branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installation_teams', to='accounts.branch', verbose_name='الفرع'),
        ),
        migrations.AddField(
            model_name='installationteam',
            name='leader',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_teams', to=settings.AUTH_USER_MODEL, verbose_name='قائد الفريق'),
        ),
        migrations.AddField(
            model_name='installationteam',
            name='members',
            field=models.ManyToManyField(related_name='installation_teams', to=settings.AUTH_USER_MODEL, verbose_name='أعضاء الفريق'),
        ),
        migrations.AddField(
            model_name='installationstep',
            name='completed_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإكمال بواسطة'),
        ),
        migrations.AddField(
            model_name='installationstep',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='installations.installation', verbose_name='عملية التركيب'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='checked_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='quality_checks', to=settings.AUTH_USER_MODEL, verbose_name='تم الفحص بواسطة'),
        ),
        migrations.AddField(
            model_name='installationqualitycheck',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='installations.installation', verbose_name='عملية التركيب'),
        ),
        migrations.AddField(
            model_name='installationnotification',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='installations.installation', verbose_name='عملية التركيب'),
        ),
        migrations.AddField(
            model_name='installationissue',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى'),
        ),
        migrations.AddField(
            model_name='installationissue',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issues', to='installations.installation', verbose_name='عملية التركيب'),
        ),
        migrations.AddField(
            model_name='installationissue',
            name='reported_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم الإبلاغ بواسطة'),
        ),
        migrations.AddField(
            model_name='installationissue',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة'),
        ),
        migrations.AddField(
            model_name='installation',
            name='team',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='installations', to='installations.installationteam', verbose_name='فريق التركيب'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['team'], name='install_team_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['status'], name='install_status_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['scheduled_date'], name='install_sched_date_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['created_at'], name='install_created_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='installationstep',
            unique_together={('installation', 'order')},
        ),
        migrations.AlterUniqueTogether(
            name='installationqualitycheck',
            unique_together={('installation', 'criteria')},
        ),
    ]
