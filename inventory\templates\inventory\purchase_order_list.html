{% extends 'inventory/inventory_base.html' %}
{% load static %}

{% block inventory_title %}طلبات الشراء{% endblock %}

{% block inventory_content %}
<div class="purchase-order-list-container">
    <!-- فلاتر البحث -->
    <div class="filters-container mb-4">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="رقم الطلب، المورد...">
                    </div>
                    <div class="col-md-3">
                        <label for="supplier" class="form-label">المورد</label>
                        <select class="form-select" id="supplier" name="supplier">
                            <option value="">جميع الموردين</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}" {% if selected_supplier == supplier.id|stringformat:"s" %}selected{% endif %}>{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="draft" {% if selected_status == 'draft' %}selected{% endif %}>مسودة</option>
                            <option value="pending" {% if selected_status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="approved" {% if selected_status == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                            <option value="partial" {% if selected_status == 'partial' %}selected{% endif %}>استلام جزئي</option>
                            <option value="received" {% if selected_status == 'received' %}selected{% endif %}>تم الاستلام</option>
                            <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="">جميع الفترات</option>
                            <option value="today" {% if selected_date_range == 'today' %}selected{% endif %}>اليوم</option>
                            <option value="week" {% if selected_date_range == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                            <option value="month" {% if selected_date_range == 'month' %}selected{% endif %}>هذا الشهر</option>
                            <option value="quarter" {% if selected_date_range == 'quarter' %}selected{% endif %}>هذا الربع</option>
                            <option value="year" {% if selected_date_range == 'year' %}selected{% endif %}>هذا العام</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:purchase_order_list' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                        <a href="{% url 'inventory:purchase_order_create' %}" class="btn btn-success float-end">
                            <i class="fas fa-plus"></i> إنشاء طلب شراء جديد
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card primary">
                <div class="stat-card-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-card-value">{{ total_orders }}</div>
                <div class="stat-card-title">إجمالي الطلبات</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card warning">
                <div class="stat-card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-card-value">{{ pending_orders }}</div>
                <div class="stat-card-title">طلبات قيد الانتظار</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-card-value">{{ received_orders }}</div>
                <div class="stat-card-title">طلبات مستلمة</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card info">
                <div class="stat-card-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-card-value">{{ total_amount|floatformat:2 }}</div>
                <div class="stat-card-title">إجمالي المبلغ</div>
            </div>
        </div>
    </div>

    <!-- قائمة طلبات الشراء -->
    <div class="row">
        <div class="col-md-12">
            <div class="data-table">
                <div class="data-table-header">
                    <h4 class="data-table-title">
                        طلبات الشراء
                        {% if purchase_orders %}
                        <span class="badge bg-primary">{{ purchase_orders|length }}</span>
                        {% endif %}
                    </h4>
                    <div class="data-table-actions">
                        <div class="dropdown">
                            <button class="btn btn-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Excel</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf"></i> PDF</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-print"></i> طباعة</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="data-table-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>المستودع</th>
                                    <th>تاريخ الطلب</th>
                                    <th>تاريخ التسليم المتوقع</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in purchase_orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                    </td>
                                    <td>{{ order.supplier.name }}</td>
                                    <td>{{ order.warehouse.name }}</td>
                                    <td>{{ order.order_date|date:"Y-m-d" }}</td>
                                    <td>{{ order.expected_date|date:"Y-m-d"|default:"-" }}</td>
                                    <td>{{ order.total_amount|floatformat:2 }}</td>
                                    <td>
                                        {% if order.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif order.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                        {% elif order.status == 'approved' %}
                                        <span class="badge bg-info">تمت الموافقة</span>
                                        {% elif order.status == 'partial' %}
                                        <span class="badge bg-primary">استلام جزئي</span>
                                        {% elif order.status == 'received' %}
                                        <span class="badge bg-success">تم الاستلام</span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'inventory:purchase_order_detail' order.id %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'inventory:purchase_order_update' order.id %}" class="btn btn-primary btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if order.status == 'draft' or order.status == 'pending' %}
                                            <a href="{% url 'inventory:purchase_order_delete' order.id %}" class="btn btn-danger btn-sm" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                            {% if order.status == 'approved' or order.status == 'partial' %}
                                            <a href="{% url 'inventory:purchase_order_receive' order.id %}" class="btn btn-success btn-sm" title="استلام">
                                                <i class="fas fa-truck-loading"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات شراء مطابقة للبحث</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- الصفحات -->
                {% if page_obj.has_other_pages %}
                <div class="data-table-footer">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_supplier %}&supplier={{ selected_supplier }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_supplier %}&supplier={{ selected_supplier }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in page_obj.paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_supplier %}&supplier={{ selected_supplier }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}">{{ i }}</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_supplier %}&supplier={{ selected_supplier }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_supplier %}&supplier={{ selected_supplier }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
