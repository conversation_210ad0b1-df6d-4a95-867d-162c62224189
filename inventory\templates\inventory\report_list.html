{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تقارير المخزون{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item active" aria-current="page">تقارير المخزون</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:low_stock_report' %}" class="btn btn-warning btn-sm">
    <i class="fas fa-exclamation-triangle"></i> تقرير المخزون المنخفض
</a>
<a href="{% url 'inventory:stock_movement_report' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-exchange-alt"></i> تقرير حركة المخزون
</a>
{% endblock %}

{% block inventory_content %}
<div class="report-list-container">
    <!-- بطاقات التقارير -->
    <div class="icon-cards-container">
        <a href="{% url 'inventory:low_stock_report' %}" class="icon-card">
            <div class="icon-card-icon" style="color: #e74a3b;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="icon-card-title">تقرير المخزون المنخفض</div>
            <div class="icon-card-subtitle">عرض المنتجات منخفضة المخزون</div>
        </a>
        
        <a href="{% url 'inventory:stock_movement_report' %}" class="icon-card">
            <div class="icon-card-icon" style="color: #4e73df;">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="icon-card-title">تقرير حركة المخزون</div>
            <div class="icon-card-subtitle">تتبع حركة المنتجات</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #1cc88a;">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="icon-card-title">تقرير أداء المنتجات</div>
            <div class="icon-card-subtitle">تحليل أداء المنتجات</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #f6c23e;">
                <i class="fas fa-calendar-times"></i>
            </div>
            <div class="icon-card-title">تقرير انتهاء الصلاحية</div>
            <div class="icon-card-subtitle">المنتجات التي قاربت على انتهاء الصلاحية</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #36b9cc;">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="icon-card-title">تقرير المستودعات</div>
            <div class="icon-card-subtitle">حالة المستودعات ونسبة الإشغال</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #6f42c1;">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="icon-card-title">تقرير طلبات الشراء</div>
            <div class="icon-card-subtitle">تحليل طلبات الشراء</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #fd7e14;">
                <i class="fas fa-truck"></i>
            </div>
            <div class="icon-card-title">تقرير الموردين</div>
            <div class="icon-card-subtitle">تحليل أداء الموردين</div>
        </a>
        
        <a href="#" class="icon-card">
            <div class="icon-card-icon" style="color: #20c9a6;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="icon-card-title">تقرير التكلفة</div>
            <div class="icon-card-subtitle">تحليل تكلفة المخزون</div>
        </a>
    </div>

    <!-- تقرير المخزون المنخفض -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                المنتجات منخفضة المخزون
                {% if low_stock_products %}
                <span class="badge bg-primary">{{ low_stock_products|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <a href="{% url 'inventory:low_stock_report' %}" class="btn btn-primary btn-sm">
                    عرض التقرير الكامل
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكود</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in low_stock_products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.code }}</td>
                            <td>{{ product.category }}</td>
                            <td>
                                <span class="badge bg-danger">{{ product.current_stock_calc }}</span>
                            </td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد منتجات منخفضة المخزون</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- آخر حركات المخزون -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                آخر حركات المخزون
                {% if recent_transactions %}
                <span class="badge bg-primary">{{ recent_transactions|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <a href="{% url 'inventory:stock_movement_report' %}" class="btn btn-primary btn-sm">
                    عرض التقرير الكامل
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المنتج</th>
                            <th>نوع الحركة</th>
                            <th>الكمية</th>
                            <th>السبب</th>
                            <th>بواسطة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in recent_transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"Y-m-d H:i" }}</td>
                            <td>{{ transaction.product.name }}</td>
                            <td>
                                <span class="badge {% if transaction.transaction_type == 'in' %}bg-success{% elif transaction.transaction_type == 'out' %}bg-danger{% else %}bg-info{% endif %}">
                                    {{ transaction.get_transaction_type_display }}
                                </span>
                            </td>
                            <td>{{ transaction.quantity }}</td>
                            <td>{{ transaction.get_reason_display }}</td>
                            <td>{{ transaction.created_by.get_full_name }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد حركات مخزون حديثة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
