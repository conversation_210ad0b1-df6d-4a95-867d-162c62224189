{% extends 'base.html' %}

{% block title %}خطوط الإنتاج - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">خطوط الإنتاج</h2>
            <p>إجمالي خطوط الإنتاج: {{ total_production_lines }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_line_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة خط إنتاج جديد
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'factory:production_line_list' %}">
                <div class="row">
                    <div class="col-md-8 mb-2">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="البحث عن خط إنتاج..." value="{{ search_query }}">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2 text-end">
                        {% if search_query %}
                            <a href="{% url 'factory:production_line_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                <i class="fas fa-times"></i> إلغاء البحث
                            </a>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Production Lines List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h5 class="mb-0"><i class="fas fa-industry"></i> قائمة خطوط الإنتاج</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row">
                    {% for line in page_obj %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100" style="border-color: var(--neutral);">
                                <div class="card-header {% if line.is_active %}bg-success text-white{% else %}bg-warning text-dark{% endif %}">
                                    <h5 class="mb-0">{{ line.name }}</h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">{{ line.description|truncatechars:100 }}</p>
                                    <p class="card-text">
                                        <strong>الحالة:</strong> 
                                        <span class="badge {% if line.is_active %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                            {% if line.is_active %}نشط{% else %}غير نشط{% endif %}
                                        </span>
                                    </p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100">
                                        <a href="{% url 'factory:production_line_detail' line.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="{% url 'factory:production_line_update' line.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{% url 'factory:production_line_delete' line.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for i in page_obj.paginator.page_range %}
                                {% if page_obj.number == i %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ i }}</span>
                                    </li>
                                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <p class="mb-3">لا توجد خطوط إنتاج متاحة{% if search_query %} تطابق بحثك عن "{{ search_query }}"{% endif %}.</p>
                    {% if search_query %}
                        <a href="{% url 'factory:production_line_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <i class="fas fa-times"></i> إلغاء البحث
                        </a>
                    {% else %}
                        <a href="{% url 'factory:production_line_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                            <i class="fas fa-plus"></i> إضافة خط إنتاج جديد
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
