{% load i18n report_math_filters %}

<div class="row">
    <!-- Sales Analysis -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{% trans 'تحليل المبيعات' %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Monthly Trends -->
                    <div class="col-md-8">
                        <canvas id="monthlySalesChart"></canvas>
                    </div>
                    <!-- Daily Patterns -->
                    <div class="col-md-4">
                        <canvas id="dailyPatternsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Metrics -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{% trans 'مؤشرات الأداء الرئيسية' %}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="border rounded p-3 text-center">
                            <h6>{% trans 'نمو المبيعات' %}</h6>
                            <h3 class="mb-0">{{ data.kpi_metrics.sales_growth|floatformat:1 }}%</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3 text-center">
                            <h6>{% trans 'معدل الاحتفاظ بالعملاء' %}</h6>
                            <h3 class="mb-0">{{ data.kpi_metrics.customer_retention|floatformat:1 }}%</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3 text-center">
                            <h6>{% trans 'متوسط وقت الإتمام' %}</h6>
                            <h3 class="mb-0">{{ data.kpi_metrics.avg_fulfillment_time|floatformat:1 }} {% trans 'ساعة' %}</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3 text-center">
                            <h6>{% trans 'هامش الربح' %}</h6>
                            <h3 class="mb-0">{{ data.kpi_metrics.profit_margin|floatformat:1 }}%</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Insights -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">{% trans 'تحليل شرائح العملاء' %}</h5>
            </div>
            <div class="card-body">
                <canvas id="customerSegmentsChart"></canvas>
                <div class="mt-3">
                    <h6>{% trans 'توزيع العملاء:' %}</h6>
                    <ul class="list-unstyled">
                        <li>VIP: {{ data.customer_insights.customer_segments.summary.vip_count }}</li>
                        <li>{% trans 'عملاء منتظمون' %}: {{ data.customer_insights.customer_segments.summary.regular_count }}</li>
                        <li>{% trans 'عملاء عرضيون' %}: {{ data.customer_insights.customer_segments.summary.occasional_count }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Health -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">{% trans 'الصحة المالية' %}</h5>
            </div>
            <div class="card-body">
                <canvas id="cashFlowChart"></canvas>
                <div class="mt-3 text-center">
                    <div class="row">
                        <div class="col-6">
                            <h6>{% trans 'نمو الإيرادات' %}</h6>
                            <h4>{{ data.financial_health.revenue_growth|floatformat:1 }}%</h4>
                        </div>
                        <div class="col-6">
                            <h6>{% trans 'هامش الربح' %}</h6>
                            <h4>{{ data.financial_health.profit_margin|floatformat:1 }}%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Sales Chart
    const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
    new Chart(monthlySalesCtx, {
        type: 'line',
        data: {
            labels: {{ data.sales_analysis.monthly_trends|map:'month'|safe }},
            datasets: [{
                label: '{% trans "المبيعات" %}',
                data: {{ data.sales_analysis.monthly_trends|map:'total_sales'|safe }},
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        }
    });

    // Daily Patterns Chart
    const dailyPatternsCtx = document.getElementById('dailyPatternsChart').getContext('2d');
    new Chart(dailyPatternsCtx, {
        type: 'bar',
        data: {
            labels: ['{% trans "الأحد" %}', '{% trans "الإثنين" %}', '{% trans "الثلاثاء" %}', '{% trans "الأربعاء" %}', '{% trans "الخميس" %}', '{% trans "الجمعة" %}', '{% trans "السبت" %}'],
            datasets: [{
                label: '{% trans "متوسط المبيعات" %}',
                data: {{ data.sales_analysis.daily_patterns|map:'avg_sales'|safe }},
                backgroundColor: 'rgba(54, 162, 235, 0.5)'
            }]
        }
    });

    // Customer Segments Chart
    const customerSegmentsCtx = document.getElementById('customerSegmentsChart').getContext('2d');
    new Chart(customerSegmentsCtx, {
        type: 'pie',
        data: {
            labels: ['VIP', '{% trans "منتظم" %}', '{% trans "عرضي" %}'],
            datasets: [{
                data: [
                    {{ data.customer_insights.customer_segments.summary.vip_count }},
                    {{ data.customer_insights.customer_segments.summary.regular_count }},
                    {{ data.customer_insights.customer_segments.summary.occasional_count }}
                ],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
            }]
        }
    });

    // Cash Flow Chart
    const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
    new Chart(cashFlowCtx, {
        type: 'bar',
        data: {
            labels: {{ data.financial_health.cash_flow|map:'month'|safe }},
            datasets: [{
                label: '{% trans "التدفق النقدي" %}',
                data: {{ data.financial_health.cash_flow|map:'net_flow'|safe }},
                backgroundColor: function(context) {
                    const value = context.dataset.data[context.dataIndex];
                    return value < 0 ? 'rgba(255, 99, 132, 0.5)' : 'rgba(75, 192, 192, 0.5)';
                }
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}