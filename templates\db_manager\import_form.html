{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{% trans "استيراد قاعدة بيانات" %}{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق منطقة السحب والإفلات */
    .dropzone {
        border: 2px dashed #007bff;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .dropzone.highlight {
        border-color: #0056b3;
        background-color: #e8f4ff;
    }

    .dropzone .icon {
        font-size: 48px;
        color: #007bff;
        margin-bottom: 15px;
    }

    .dropzone.highlight .icon {
        color: #0056b3;
    }

    .dropzone p {
        margin: 0;
        font-size: 16px;
        color: #007bff;
    }

    .dropzone.highlight p {
        color: #0056b3;
    }

    /* إخفاء حقل الملف الأصلي */
    .file-input-hidden {
        position: absolute;
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        z-index: -1;
    }

    /* تنسيق عرض الملف المختار */
    .file-info {
        display: none;
        padding: 15px;
        border-radius: 8px;
        background-color: #e8f4ff;
        margin-top: 15px;
        position: relative;
    }

    .file-info .file-name {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 5px;
        display: block;
    }

    .file-info .file-size {
        color: #6c757d;
        font-size: 14px;
    }

    .file-info .file-icon {
        font-size: 24px;
        margin-right: 10px;
        color: #007bff;
    }

    /* زر إزالة الملف */
    .remove-file {
        color: #dc3545;
        cursor: pointer;
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 18px;
    }

    /* تنسيق زر الاستيراد */
    .btn-import {
        padding: 10px 25px;
        font-size: 16px;
    }

    .import-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    .import-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .import-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .import-section h4 {
        margin-bottom: 20px;
        color: #007bff;
    }

    .import-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="import-container">
        <div class="import-header">
            <h1>{% trans "استيراد قاعدة بيانات" %}</h1>
            <p class="lead">{% trans "قم بتحميل ملف JSON أو SQL أو DUMP لاستيراده إلى قاعدة البيانات" %}</p>
            {% if default_db %}
                <div class="alert alert-info">
                    <i class="fas fa-database"></i>
                    {% trans "قاعدة البيانات الحالية:" %} <strong>{{ default_db.name }}</strong>
                    <span class="badge badge-primary">PostgreSQL</span>
                </div>
            {% endif %}
        </div>

        <form method="post" enctype="multipart/form-data" id="importForm">
            {% csrf_token %}

            <div class="import-section">
                <h4>{% trans "اختر قاعدة البيانات" %}</h4>

                <div class="form-group">
                    <label for="{{ form.database_config.id_for_label }}">{% trans "قاعدة البيانات" %}</label>
                    {% if default_db %}
                        <div class="alert alert-primary mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-database me-2"></i>
                                <div>
                                    <strong>{% trans "قاعدة البيانات الحالية:" %} {{ default_db.name }}</strong>
                                    <div><small>{% trans "تم تحديد قاعدة البيانات الافتراضية تلقائيًا" %}</small></div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    {{ form.database_config|add_class:"form-control" }}
                    <small class="form-text text-muted">{% trans "اختر قاعدة البيانات التي تريد استيراد البيانات إليها" %}</small>
                    {% if form.database_config.errors %}
                        <div class="invalid-feedback d-block">{{ form.database_config.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="import-section">
                <h4>{% trans "ملف الاستيراد" %}</h4>

                <!-- منطقة السحب والإفلات -->
                <div id="dropzone" class="dropzone">
                    <div class="icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <p>{% trans "اسحب وأفلت الملف هنا أو انقر للاختيار" %}</p>
                    <p class="small text-muted mt-2">{% trans "الملفات المدعومة: JSON, DUMP, SQL" %}</p>
                </div>

                <!-- حقل الملف المخفي -->
                {{ form.file|add_class:"file-input-hidden" }}

                <!-- عرض معلومات الملف المختار -->
                <div id="file-info" class="file-info">
                    <i class="fas fa-file-alt file-icon"></i>
                    <span class="file-name"></span>
                    <span class="file-size"></span>
                    <i class="fas fa-times remove-file" title="{% trans 'إزالة الملف' %}"></i>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> {% trans "يمكنك استيراد ملفات بتنسيق JSON أو SQL أو DUMP. تأكد من أن الملف متوافق مع هيكل قاعدة البيانات الحالية." %}
                </div>

                <small class="form-text text-muted mt-2">{{ form.file.help_text }}</small>
                {% if form.file.errors %}
                    <div class="invalid-feedback d-block">{{ form.file.errors }}</div>
                {% endif %}

                <div class="form-check mt-3">
                    {{ form.clear_data|add_class:"form-check-input" }}
                    <label class="form-check-label" for="{{ form.clear_data.id_for_label }}">
                        {{ form.clear_data.label }}
                    </label>
                    <small class="form-text text-muted d-block">{{ form.clear_data.help_text }}</small>
                    {% if form.clear_data.errors %}
                        <div class="invalid-feedback d-block">{{ form.clear_data.errors }}</div>
                    {% endif %}
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i> {% trans "تحذير: إذا قمت بتفعيل خيار حذف البيانات القديمة، سيتم حذف معظم البيانات الموجودة في قاعدة البيانات قبل استيراد البيانات الجديدة. لن يتم حذف بيانات المستخدمين والأدوار وإعدادات قواعد البيانات. هذه العملية لا يمكن التراجع عنها." %}
                </div>
            </div>

            <div class="import-footer">
                <button type="submit" class="btn btn-primary btn-lg">{% trans "استيراد" %}</button>
                <a href="{% url 'db_manager:database_list' %}" class="btn btn-secondary btn-lg">{% trans "إلغاء" %}</a>

                <hr class="my-4">

                <div class="text-center">
                    <p class="text-muted mb-2">{% trans "إذا كنت تواجه مشكلة في تكرار المعرفات أو أخطاء أخرى في الاستيراد، يمكنك إعادة ضبط سجلات الاستيراد:" %}</p>
                    <a href="{% url 'db_manager:database_import' %}?reset_imports=true" class="btn btn-danger" onclick="return confirm('{% trans "هل أنت متأكد من حذف جميع سجلات الاستيراد؟ هذه العملية لا يمكن التراجع عنها." %}');">
                        <i class="fas fa-trash-alt me-2"></i> {% trans "حذف جميع سجلات الاستيراد وإعادة ضبط التسلسل" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // الحصول على العناصر
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.querySelector('.file-input-hidden');
        const fileInfo = document.getElementById('file-info');
        const fileName = fileInfo.querySelector('.file-name');
        const fileSize = fileInfo.querySelector('.file-size');
        const removeFile = fileInfo.querySelector('.remove-file');

        // منع السلوك الافتراضي للسحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // إضافة تأثيرات السحب
        ['dragenter', 'dragover'].forEach(eventName => {
            dropzone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropzone.classList.add('highlight');
        }

        function unhighlight() {
            dropzone.classList.remove('highlight');
        }

        // معالجة إفلات الملف
        dropzone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length) {
                fileInput.files = files;
                updateFileInfo(files[0]);
            }
        }

        // معالجة النقر على منطقة السحب والإفلات
        dropzone.addEventListener('click', function() {
            fileInput.click();
        });

        // معالجة اختيار الملف
        fileInput.addEventListener('change', function() {
            if (this.files.length) {
                updateFileInfo(this.files[0]);
            }
        });

        // تحديث معلومات الملف
        function updateFileInfo(file) {
            // التحقق من نوع الملف
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!['json', 'dump', 'sql'].includes(fileExtension)) {
                alert('{% trans "يجب أن يكون الملف بتنسيق JSON أو DUMP أو SQL فقط" %}');
                resetFileInput();
                return;
            }

            console.log('File extension accepted:', fileExtension);

            // التحقق من حجم الملف (100 ميجابايت كحد أقصى)
            if (file.size > 100 * 1024 * 1024) {
                alert('{% trans "حجم الملف كبير جدًا. الحد الأقصى هو 100 ميجابايت" %}');
                resetFileInput();
                return;
            }

            // عرض معلومات الملف
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';

            // تغيير أيقونة الملف حسب النوع
            const fileIcon = fileInfo.querySelector('.file-icon');
            if (fileExtension === 'json') {
                fileIcon.className = 'fas fa-file-code file-icon';
            } else if (fileExtension === 'sql') {
                fileIcon.className = 'fas fa-database file-icon';
            } else {
                fileIcon.className = 'fas fa-file-alt file-icon';
            }

            console.log('Selected file:', file.name, formatFileSize(file.size));
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' bytes';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
        }

        // إزالة الملف
        removeFile.addEventListener('click', function() {
            resetFileInput();
        });

        // إعادة ضبط حقل الملف
        function resetFileInput() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
        }

        // التحقق من صحة النموذج قبل الإرسال
        $('#importForm').on('submit', function(e) {
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('{% trans "يرجى اختيار ملف للاستيراد" %}');
                return false;
            }

            // عرض رسالة تحميل
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i> {% trans "جاري الاستيراد..." %}');

            return true;
        });
    });
</script>
{% endblock %}
