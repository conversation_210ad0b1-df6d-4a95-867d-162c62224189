{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans 'التقارير' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{% trans 'التقارير' %}</h2>
        <a href="{% url 'reports:report_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans 'إنشاء تقرير جديد' %}
        </a>
    </div>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if reports %}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{% trans 'عنوان التقرير' %}</th>
                    <th>{% trans 'نوع التقرير' %}</th>
                    <th>{% trans 'تاريخ الإنشاء' %}</th>
                    <th>{% trans 'آخر تحديث' %}</th>
                    <th>{% trans 'الإجراءات' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for report in reports %}
                <tr>
                    <td>{{ report.title }}</td>
                    <td>{{ report.get_report_type_display }}</td>
                    <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                    <td>{{ report.updated_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'reports:report_detail' report.pk %}" class="btn btn-info btn-sm" title="{% trans 'عرض التقرير' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'reports:report_update' report.pk %}" class="btn btn-warning btn-sm" title="{% trans 'تعديل التقرير' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'reports:report_delete' report.pk %}" class="btn btn-danger btn-sm" title="{% trans 'حذف التقرير' %}">
                                <i class="fas fa-trash"></i>
                            </a>
                            <a href="{% url 'reports:schedule_create' report.pk %}" class="btn btn-success btn-sm" title="{% trans 'جدولة التقرير' %}">
                                <i class="fas fa-clock"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        {% trans 'لا توجد تقارير حتى الآن.' %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[title]').tooltip();
        
        // Fade out alerts after 3 seconds
        $('.alert').delay(3000).fadeOut(500);
    });
</script>
{% endblock %}
