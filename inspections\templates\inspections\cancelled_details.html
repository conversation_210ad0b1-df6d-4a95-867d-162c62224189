{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans 'تفاصيل المعاينات الملغاة' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">{% trans 'تفاصيل المعاينات الملغاة' %}</h2>
        <a href="{% url 'inspections:inspection_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> {% trans 'عودة لقائمة المعاينات' %}
        </a>
    </div>
    {% if inspections %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle">
            <thead class="table-light">
                <tr>
                    <th>{% trans 'رقم العقد' %}</th>
                    <th>{% trans 'العميل' %}</th>
                    <th>{% trans 'العنوان' %}</th>
                    <th>{% trans 'تاريخ الطلب' %}</th>
                    <th>{% trans 'تاريخ التنفيذ' %}</th>
                    <th>{% trans 'الملاحظات' %}</th>
                    <th>{% trans 'الإجراءات' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for inspection in inspections %}
                <tr>
                    <td>{{ inspection.contract_number }}</td>
                    <td>{{ inspection.customer|default:'-' }}</td>
                    <td>{{ inspection.customer.address|default:'-' }}</td>
                    <td>{{ inspection.request_date }}</td>
                    <td>{{ inspection.scheduled_date }}</td>
                    <td>{{ inspection.notes|default:'-' }}</td>
                    <td>
                        <a href="{% url 'inspections:inspection_detail' inspection.pk %}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> {% trans 'عرض التفاصيل' %}
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info text-center my-4">
        {% trans 'لا توجد معاينات ملغاة حالياً.' %}
    </div>
    {% endif %}
    {% if is_paginated %}
    <nav aria-label="{% trans 'تصفح الصفحات' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
            {% for num in paginator.page_range %}
            <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endfor %}
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}
