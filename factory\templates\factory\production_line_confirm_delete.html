{% extends 'base.html' %}

{% block title %}حذف {{ production_line.name }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">حذف خط الإنتاج</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_line_list' %}">خطوط الإنتاج</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_line_detail' production_line.pk %}">{{ production_line.name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حذف</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_line_detail' production_line.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى التفاصيل
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--alert);">
                <div class="card-header" style="background-color: var(--alert); color: white;">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> تأكيد الحذف</h5>
                </div>
                <div class="card-body text-center">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h4>هل أنت متأكد من حذف خط الإنتاج "{{ production_line.name }}"؟</h4>
                        <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه، وسيؤدي إلى حذف جميع البيانات المرتبطة بهذا الخط.</p>
                    </div>
                    
                    <div class="card mb-4" style="border-color: var(--neutral);">
                        <div class="card-header" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <h6 class="mb-0">معلومات خط الإنتاج</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%;">الاسم</th>
                                    <td>{{ production_line.name }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <span class="badge {% if production_line.is_active %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                            {% if production_line.is_active %}نشط{% else %}غير نشط{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>عدد أوامر الإنتاج</th>
                                    <td>{{ production_line.production_orders.count }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn" style="background-color: var(--alert); color: white;">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                        <a href="{% url 'factory:production_line_detail' production_line.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
