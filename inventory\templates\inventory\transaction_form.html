{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تسجيل حركة مخزون - {{ product.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">{% trans "المخزون" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'inventory:product_detail' product.pk %}">{{ product.name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{% trans "تسجيل حركة مخزون" %}</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-exchange-alt"></i> {% trans "تسجيل حركة مخزون" %} - {{ product.name }}</h4>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">{% trans "معلومات المنتج" %}</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>{% trans "الكود:" %}</strong> {{ product.code }}</p>
                            <p><strong>{% trans "الاسم:" %}</strong> {{ product.name }}</p>
                            <p><strong>{% trans "الفئة:" %}</strong> {{ product.category.name }}</p>
                            <p><strong>{% trans "وحدة القياس:" %}</strong> {{ product.get_unit_display }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">{% trans "حالة المخزون" %}</h5>
                        </div>
                        <div class="card-body">                            <div class="text-center mb-3">
                                <h3>{{ current_stock }} {{ product.get_unit_display }}</h3>
                                <p class="mb-0">                                {% if current_stock > 0 %}
                                        <span class="badge bg-success">{% trans "متوفر للصرف" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "غير متوفر" %}</span>
                                    {% endif %}
                                </p>
                            </div>                            <div class="progress" style="height: 20px;">
                                {% if current_stock > 0 %}
                                    {% with percent=current_stock|divisibleby:product.minimum_stock|yesno:"100,50" %}
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ percent }}%;" aria-valuenow="{{ percent }}" aria-valuemin="0" aria-valuemax="100">
                                        {{ current_stock }}
                                    </div>
                                    {% endwith %}
                                {% else %}
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                                        {% trans "متوفر" %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="transaction_type" class="form-label">{% trans "نوع الحركة" %} *</label>
                        <select class="form-select" id="transaction_type" name="transaction_type" required>
                            <option value="">{% trans "اختر نوع الحركة" %}</option>
                            {% for type_value, type_name in transaction_types %}
                                <option value="{{ type_value }}">{{ type_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="reason" class="form-label">{% trans "السبب" %} *</label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">{% trans "اختر السبب" %}</option>
                            {% for reason_value, reason_name in transaction_reasons %}
                                <option value="{{ reason_value }}">{{ reason_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="quantity" class="form-label">{% trans "الكمية" %} *</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                            <span class="input-group-text">{{ product.get_unit_display }}</span>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference"
                               placeholder="{% trans 'مثال: رقم الفاتورة، رقم الطلب، إلخ' %}">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{% url 'inventory:product_detail' product.pk %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "العودة" %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% trans "تسجيل الحركة" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const transactionTypeSelect = document.getElementById('transaction_type');
        const quantityInput = document.getElementById('quantity');
        const currentStock = {{ product.current_stock_calc }};

        // Add validation for outgoing transactions
        transactionTypeSelect.addEventListener('change', function() {
            if (this.value === 'out') {
                quantityInput.setAttribute('max', currentStock);

                // Add validation message
                const validationMessage = document.createElement('div');
                validationMessage.className = 'form-text text-danger';
                validationMessage.id = 'quantity-validation';
                validationMessage.textContent = '{% trans "الكمية المتاحة للصرف:" %} ' + currentStock;

                // Remove existing message if any
                const existingMessage = document.getElementById('quantity-validation');
                if (existingMessage) {
                    existingMessage.remove();
                }

                // Add message after quantity input
                quantityInput.parentNode.after(validationMessage);
            } else {
                // Remove max attribute and validation message for incoming transactions
                quantityInput.removeAttribute('max');

                const existingMessage = document.getElementById('quantity-validation');
                if (existingMessage) {
                    existingMessage.remove();
                }
            }
        });

        // Validate quantity on input
        quantityInput.addEventListener('input', function() {
            if (transactionTypeSelect.value === 'out' && parseInt(this.value) > currentStock) {
                this.setCustomValidity('{% trans "الكمية المطلوبة أكبر من المخزون المتاح" %}');
            } else {
                this.setCustomValidity('');
            }
        });
    });
</script>
{% endblock %}
