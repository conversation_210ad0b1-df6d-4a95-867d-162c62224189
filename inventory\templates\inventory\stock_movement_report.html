{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تقرير حركة المخزون{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:report_list' %}">التقارير</a></li>
<li class="breadcrumb-item active" aria-current="page">تقرير حركة المخزون</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:report_list' %}" class="btn btn-secondary btn-sm">
    <i class="fas fa-arrow-left"></i> العودة للتقارير
</a>
<a href="{% url 'inventory:low_stock_report' %}" class="btn btn-warning btn-sm">
    <i class="fas fa-exclamation-triangle"></i> تقرير المخزون المنخفض
</a>
{% endblock %}

{% block inventory_content %}
<div class="stock-movement-report-container">
    <!-- إحصائيات سريعة -->
    <div class="stats-cards-container">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-arrow-circle-down"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">إجمالي الوارد</div>
                <div class="stat-card-value">{{ total_in }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 15% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-arrow-circle-up"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">إجمالي الصادر</div>
                <div class="stat-card-value">{{ total_out }}</div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-up"></i> 8% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">صافي التغيير</div>
                <div class="stat-card-value">{{ net_change }}</div>
                <div class="stat-card-change {% if net_change >= 0 %}positive{% else %}negative{% endif %}">
                    <i class="fas fa-{% if net_change >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                    {% if net_change >= 0 %}زيادة{% else %}نقص{% endif %} في المخزون
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">عدد الحركات</div>
                <div class="stat-card-value">{{ transactions|length }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 5% منذ الأسبوع الماضي
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filters-container mb-4">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم المنتج، الكود، المرجع...">
                    </div>
                    <div class="col-md-3">
                        <label for="product" class="form-label">المنتج</label>
                        <select class="form-select" id="product" name="product">
                            <option value="">جميع المنتجات</option>
                            {% for product in products %}
                            <option value="{{ product.id }}" {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">نوع الحركة</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">جميع الأنواع</option>
                            <option value="in" {% if selected_type == 'in' %}selected{% endif %}>وارد</option>
                            <option value="out" {% if selected_type == 'out' %}selected{% endif %}>صادر</option>
                            <option value="adjustment" {% if selected_type == 'adjustment' %}selected{% endif %}>تسوية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="">جميع الفترات</option>
                            <option value="today" {% if selected_date_range == 'today' %}selected{% endif %}>اليوم</option>
                            <option value="week" {% if selected_date_range == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                            <option value="month" {% if selected_date_range == 'month' %}selected{% endif %}>هذا الشهر</option>
                            <option value="quarter" {% if selected_date_range == 'quarter' %}selected{% endif %}>هذا الربع</option>
                            <option value="year" {% if selected_date_range == 'year' %}selected{% endif %}>هذا العام</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:stock_movement_report' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                        <a href="#" class="btn btn-success float-end" id="exportBtn">
                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- رسم بياني لحركة المخزون -->
    <div class="chart-container mb-4">
        <div class="chart-header">
            <h4 class="chart-title">حركة المخزون</h4>
            <div class="chart-actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary active" data-period="week">أسبوع</button>
                    <button type="button" class="btn btn-outline-secondary" data-period="month">شهر</button>
                    <button type="button" class="btn btn-outline-secondary" data-period="quarter">ربع سنة</button>
                    <button type="button" class="btn btn-outline-secondary" data-period="year">سنة</button>
                </div>
            </div>
        </div>
        <div class="chart-body">
            <canvas id="stockMovementChart" height="300"></canvas>
        </div>
    </div>

    <!-- جدول حركات المخزون -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                حركات المخزون
                {% if transactions %}
                <span class="badge bg-primary">{{ transactions|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <a href="#" class="btn btn-primary btn-sm" id="printBtn">
                    <i class="fas fa-print"></i> طباعة
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المنتج</th>
                            <th>نوع الحركة</th>
                            <th>الكمية</th>
                            <th>السبب</th>
                            <th>المرجع</th>
                            <th>بواسطة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'inventory:product_detail' transaction.product.id %}">{{ transaction.product.name }}</a>
                            </td>
                            <td>
                                <span class="badge {% if transaction.transaction_type == 'in' %}bg-success{% elif transaction.transaction_type == 'out' %}bg-danger{% else %}bg-info{% endif %}">
                                    {{ transaction.get_transaction_type_display }}
                                </span>
                            </td>
                            <td>{{ transaction.quantity }}</td>
                            <td>{{ transaction.get_reason_display }}</td>
                            <td>{{ transaction.reference|default:"-" }}</td>
                            <td>{{ transaction.created_by.get_full_name }}</td>
                            <td>{{ transaction.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد حركات مخزون مطابقة للبحث</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net@1.13.1/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.1/js/dataTables.bootstrap5.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة جدول البيانات
        $('.datatable').DataTable({
            language: {
                processing: "جارٍ التحميل...",
                search: "بحث:",
                lengthMenu: "عرض _MENU_ سجلات",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ سجل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 سجل",
                infoFiltered: "(منتقاة من مجموع _MAX_ سجل)",
                infoPostFix: "",
                loadingRecords: "جارٍ التحميل...",
                zeroRecords: "لم يعثر على أية سجلات",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                },
                aria: {
                    sortAscending: ": تفعيل لترتيب العمود تصاعدياً",
                    sortDescending: ": تفعيل لترتيب العمود تنازلياً"
                }
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            order: [[0, 'desc']],
            pageLength: 10
        });
        
        // بيانات الرسم البياني
        const stockMovementData = {
            week: {
                labels: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                inData: [10, 15, 8, 12, 20, 5, 18],
                outData: [5, 10, 12, 8, 15, 3, 10]
            },
            month: {
                labels: ['1', '5', '10', '15', '20', '25', '30'],
                inData: [50, 65, 80, 120, 90, 75, 100],
                outData: [30, 45, 60, 80, 70, 50, 85]
            },
            quarter: {
                labels: ['يناير', 'فبراير', 'مارس'],
                inData: [200, 250, 300],
                outData: [150, 200, 250]
            },
            year: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                inData: [200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750],
                outData: [150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700]
            }
        };
        
        // رسم بياني لحركة المخزون
        const ctx = document.getElementById('stockMovementChart').getContext('2d');
        let stockMovementChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: stockMovementData.week.labels,
                datasets: [
                    {
                        label: 'وارد',
                        data: stockMovementData.week.inData,
                        borderColor: '#1cc88a',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#1cc88a',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        fill: true
                    },
                    {
                        label: 'صادر',
                        data: stockMovementData.week.outData,
                        borderColor: '#e74a3b',
                        backgroundColor: 'rgba(231, 74, 59, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#e74a3b',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#858796'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            zeroLineColor: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#858796',
                            beginAtZero: true
                        }
                    }
                },
                plugins: {
                    legend: {
                        rtl: true,
                        position: 'top'
                    },
                    tooltip: {
                        rtl: true,
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
        
        // تغيير فترة الرسم البياني
        const periodButtons = document.querySelectorAll('[data-period]');
        periodButtons.forEach(button => {
            button.addEventListener('click', function() {
                const period = this.getAttribute('data-period');
                
                // تحديث حالة الأزرار
                periodButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // تحديث بيانات الرسم البياني
                stockMovementChart.data.labels = stockMovementData[period].labels;
                stockMovementChart.data.datasets[0].data = stockMovementData[period].inData;
                stockMovementChart.data.datasets[1].data = stockMovementData[period].outData;
                stockMovementChart.update();
            });
        });
        
        // زر تصدير البيانات
        document.getElementById('exportBtn').addEventListener('click', function(e) {
            e.preventDefault();
            alert('سيتم تصدير البيانات إلى ملف Excel');
            // هنا يمكن إضافة كود لتصدير البيانات
        });
        
        // زر الطباعة
        document.getElementById('printBtn').addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });
    });
</script>
{% endblock %}
