@echo off
title PRODUCTION SERVER - NO PUSH ALLOWED
color 0C
cls

echo.
echo ========================================
echo    ⚠️  PRODUCTION SERVER WARNING ⚠️
echo ========================================
echo.
echo This is a PRODUCTION-ONLY machine!
echo.
echo 🚫 Git push operations are NOT ALLOWED
echo 🚫 Code changes should NOT be made here
echo.
echo ✅ Use this instead:
echo    - Run website: run-elkhawaga.bat
echo    - Get updates: update-system.bat
echo    - Check status: git status
echo.
echo 💻 For development work:
echo    - Use the development machine
echo    - Push changes from there
echo    - This machine will receive updates automatically
echo.
echo ========================================
echo Press any key to exit...
pause >nul
