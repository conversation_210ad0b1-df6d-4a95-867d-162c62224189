# Generated by Django 4.2.9 on 2025-05-12 08:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_alter_product_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='stockalert',
            name='description',
            field=models.TextField(blank=True, verbose_name='الوصف'),
        ),
        migrations.AddField(
            model_name='stockalert',
            name='priority',
            field=models.CharField(choices=[('high', 'عالية'), ('medium', 'متوسطة'), ('low', 'منخفضة')], default='medium', max_length=10, verbose_name='الأولوية'),
        ),
        migrations.AlterField(
            model_name='product',
            name='code',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.Char<PERSON>ield(max_length=255),
        ),
        migrations.AlterField(
            model_name='product',
            name='price',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='alert_type',
            field=models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('expiry', 'قرب انتهاء الصلاحية'), ('out_of_stock', 'نفاد المخزون'), ('overstock', 'فائض في المخزون'), ('price_change', 'تغير في السعر')], max_length=15, verbose_name='نوع التنبيه'),
        ),
        migrations.AddIndex(
            model_name='stockalert',
            index=models.Index(fields=['priority'], name='alert_priority_idx'),
        ),
    ]
