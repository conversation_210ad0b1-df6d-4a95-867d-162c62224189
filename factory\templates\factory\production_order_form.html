{% extends 'base.html' %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">{{ title }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_list' %}">أوامر الإنتاج</a></li>
                    {% if production_order %}
                        <li class="breadcrumb-item"><a href="{% url 'factory:production_order_detail' production_order.pk %}">{{ production_order.order.order_number }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">تعديل</li>
                    {% else %}
                        <li class="breadcrumb-item active" aria-current="page">إضافة جديد</li>
                    {% endif %}
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> {{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.order.id_for_label }}" class="form-label">{{ form.order.label }}</label>
                                    {{ form.order }}
                                    {% if form.order.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.order.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">اختر الطلب الذي سيتم إنتاجه</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.production_line.id_for_label }}" class="form-label">{{ form.production_line.label }}</label>
                                    {{ form.production_line }}
                                    {% if form.production_line.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.production_line.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">اختر خط الإنتاج الذي سيتم استخدامه</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.status.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.estimated_completion.id_for_label }}" class="form-label">{{ form.estimated_completion.label }}</label>
                                    {{ form.estimated_completion }}
                                    {% if form.estimated_completion.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.estimated_completion.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">التاريخ والوقت المتوقع لانتهاء الإنتاج</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <a href="{% if production_order %}{% url 'factory:production_order_detail' production_order.pk %}{% else %}{% url 'factory:production_order_list' %}{% endif %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
