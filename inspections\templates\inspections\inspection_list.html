{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans 'المعاينات' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{% trans 'المعاينات' %}</h2>
        <div class="d-flex gap-2">
            <a href="{% url 'inspections:inspection_report_create' %}" class="btn btn-success">
                <i class="fas fa-file-alt"></i> تقرير جديد
            </a>
            <a href="{% url 'inspections:inspection_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans 'إضافة معاينة' %}
            </a>
        </div>
    </div>

    <!-- Dashboard -->
    <div class="row mb-4">
        <div class="col">
            <a href="{% url 'inspections:inspection_list' %}" class="text-decoration-none">
                <div class="card text-center shadow-sm border-primary dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-list-alt fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">{% trans 'إجمالي المعاينات' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.total_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'inspections:inspection_list' %}?status=pending&from_orders=1" class="text-decoration-none">
                <div class="card text-center shadow-sm border-warning dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">{% trans 'المعاينات الجديدة' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.new_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'inspections:inspection_list' %}?status=scheduled" class="text-decoration-none">
                <div class="card text-center shadow-sm border-info dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                        <h5 class="card-title">{% trans 'المعاينات المجدولة' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.scheduled_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'inspections:completed_details' %}" class="text-decoration-none">
                <div class="card text-center shadow-sm border-success dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5 class="card-title">{% trans 'الناجحة' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.successful_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'inspections:cancelled_details' %}" class="text-decoration-none">
                <div class="card text-center shadow-sm border-danger dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h5 class="card-title">{% trans 'الملغاة' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.cancelled_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'inspections:inspection_list' %}?is_duplicated=1" class="text-decoration-none">
                <div class="card text-center shadow-sm border-secondary dashboard-card-hover">
                    <div class="card-body">
                        <i class="fas fa-copy fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title">{% trans 'المعاينات المكررة' %}</h5>
                        <span class="display-6 fw-bold">{{ dashboard.duplicated_inspections|default:0 }}</span>
                    </div>
                </div>
            </a>
        </div>
    </div>
    <style>
    .dashboard-card-hover:hover {
        box-shadow: 0 0 0 0.2rem #0d6efd33;
        transform: scale(1.03);
        transition: 0.2s;
    }
    </style>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <input type="text" name="search" class="form-control" placeholder="{% trans 'بحث...' %}"
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <select name="branch" class="form-select">
                        <option value="">{% trans 'كل الفروع' %}</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if branch.id|stringformat:'s' == request.GET.branch|stringformat:'s' %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">{% trans 'كل الحالات' %}</option>
                        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>
                            {% trans 'قيد الانتظار' %}
                        </option>
                        <option value="scheduled" {% if request.GET.status == 'scheduled' %}selected{% endif %}>
                            {% trans 'مجدول' %}
                        </option>
                        <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>
                            {% trans 'مكتملة' %}
                        </option>
                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>
                            {% trans 'ملغية' %}
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-search"></i> {% trans 'بحث' %}
                    </button>
                    <a href="{% url 'inspections:inspection_list' %}" class="btn btn-light">
                        <i class="fas fa-times"></i> {% trans 'إعادة تعيين' %}
                    </a>
                </div>
                {% if request.GET.is_duplicated == '1' %}
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <i class="fas fa-filter"></i> {% trans 'تصفية: المعاينات المكررة فقط' %}
                        <a href="{% url 'inspections:inspection_list' %}" class="btn btn-sm btn-outline-info float-end">
                            <i class="fas fa-times"></i> {% trans 'إلغاء التصفية' %}
                        </a>
                    </div>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Inspections Table -->
    {% if inspections %}
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>{% trans 'رقم العقد' %}</th>
                    <th>{% trans 'العميل' %}</th>
                    <th>{% trans 'تاريخ الطلب' %}</th>
                    <th>{% trans 'تاريخ التنفيذ' %}</th>
                    <th>{% trans 'عدد الشبابيك' %}</th>
                    <th>{% trans 'فني المعاينة' %}</th>
                    <th>{% trans 'البائع' %}</th>
                    <th>{% trans 'ملف المعاينة' %}</th>
                    <th>{% trans 'الحالة' %}</th>
                    <th>{% trans 'النتيجة' %}</th>
                    <th>{% trans 'الإجراءات' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for inspection in inspections %}
                <tr>
                    <td>{{ inspection.contract_number }}</td>
                    <td>{{ inspection.customer|default:"عميل جديد" }}</td>
                    <td>{{ inspection.request_date }}</td>
                    <td>{{ inspection.scheduled_date }}</td>
                    <td>{{ inspection.windows_count|default:"-" }}</td>
                    <td>
                        {% if inspection.inspector %}
                            {% if inspection.inspector.get_full_name %}
                                {{ inspection.inspector.get_full_name }}
                            {% else %}
                                {{ inspection.inspector.username }}
                            {% endif %}
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td>{% if inspection.responsible_employee %}{{ inspection.responsible_employee }}{% else %}-{% endif %}</td>
                    <td>
                        {% if inspection.is_uploaded_to_drive and inspection.google_drive_file_url %}
                            <a href="{{ inspection.google_drive_file_url }}" target="_blank" title="عرض ملف المعاينة في Google Drive"
                               data-inspection-id="{{ inspection.id }}">
                                <i class="fas fa-file-pdf text-danger" style="font-size: 24px;"></i>
                            </a>
                        {% elif inspection.inspection_file %}
                            <span title="جاري رفع الملف إلى Google Drive"
                                  data-inspection-id="{{ inspection.id }}"
                                  data-upload-pending="true">
                                <i class="fas fa-file-pdf text-warning" style="font-size: 24px;"></i>
                                <i class="fas fa-clock text-warning" style="font-size: 12px; position: relative; top: -10px; left: -5px;"></i>
                            </span>
                        {% else %}
                            <i class="fas fa-file-pdf text-muted" style="font-size: 24px;" title="لا يوجد ملف"></i>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge {% if inspection.status == 'pending' %}bg-warning
                                         {% elif inspection.status == 'scheduled' %}bg-info
                                         {% elif inspection.status == 'completed' %}bg-success
                                         {% else %}bg-danger{% endif %}">
                            {{ inspection.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if inspection.result %}
                            <span class="badge {% if inspection.result == 'passed' %}bg-success{% else %}bg-danger{% endif %}">
                                {{ inspection.get_result_display }}
                            </span>
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'inspections:inspection_detail' inspection.pk %}"
                               class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if inspection.status == 'pending' %}
                            <a href="{% url 'inspections:inspection_update' inspection.pk %}"
                               class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'inspections:inspection_delete' inspection.pk %}"
                               class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                <i class="fas fa-trash"></i>
                            </a>
                            {% endif %}

                            {% if inspection.status == 'completed' %}
                            <button type="button"
                                   class="btn btn-sm btn-success duplicate-inspection"
                                   data-id="{{ inspection.pk }}"
                                   data-contract="{{ inspection.contract_number }}"
                                   data-customer="{{ inspection.customer }}"
                                   title="{% trans 'تكرار المعاينة' %}">
                                <i class="fas fa-copy"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'تصفح الصفحات' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ request.GET.search }}&status={{ request.GET.status }}&branch={{ request.GET.branch }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
            <li class="page-item {% if num == page_obj.number %}active{% endif %}">
                <a class="page-link" href="?page={{ num }}&search={{ request.GET.search }}&status={{ request.GET.status }}&branch={{ request.GET.branch }}">
                    {{ num }}
                </a>
            </li>
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ request.GET.search }}&status={{ request.GET.status }}&branch={{ request.GET.branch }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="alert alert-info">
        {% trans 'لا توجد معاينات متاحة.' %}
    </div>
    {% endif %}
</div>

<!-- Modal for Duplicate Inspection -->
<div class="modal fade" id="duplicateInspectionModal" tabindex="-1" aria-labelledby="duplicateInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="duplicateInspectionModalLabel">{% trans 'تكرار معاينة' %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="duplicate-inspection-message">{% trans 'هل تريد تكرار المعاينة' %} <span id="inspection-contract"></span> للعميل <span id="inspection-customer"></span>؟</p>
                <p>{% trans 'سيتم إنشاء معاينة جديدة بنفس بيانات المعاينة المكتملة مع تاريخ تنفيذ جديد.' %}</p>
                <div class="form-group mb-3">
                    <label for="scheduled_date" class="form-label">{% trans 'تاريخ التنفيذ الجديد' %}</label>
                    <input type="date" class="form-control" id="scheduled_date" name="scheduled_date" required>
                </div>
                <div class="form-group mb-3">
                    <label for="duplicate-notes" class="form-label">{% trans 'ملاحظات إضافية' %}</label>
                    <textarea class="form-control" id="duplicate-notes" rows="3"></textarea>
                </div>
                <div class="alert alert-info">
                    {% trans 'سيتم نقل جميع بيانات المعاينة الأصلية إلى المعاينة الجديدة.' %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans 'إلغاء' %}</button>
                <button type="button" class="btn btn-success" id="confirm-duplicate">{% trans 'تكرار المعاينة' %}</button>
            </div>
            <div class="modal-loading d-none text-center p-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{% trans 'جاري التحميل...' %}</span>
                </div>
                <p>{% trans 'جاري إنشاء المعاينة الجديدة...' %}</p>
            </div>
        </div>
    </div>
</div>

<!-- تنبيه النجاح -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>
                <span id="successToastMessage">{% trans 'تم تكرار المعاينة بنجاح' %}</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    </div>
</div>

<!-- تنبيه الخطأ -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999">
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span id="errorToastMessage">{% trans 'حدث خطأ أثناء تكرار المعاينة' %}</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'inspections/js/upload_status_checker.js' %}"></script>
<script>
    $(document).ready(function() {
        // Enable Bootstrap tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Set default scheduled date to tomorrow
        var tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        var formattedDate = tomorrow.toISOString().split('T')[0];
        $('#scheduled_date').val(formattedDate);

        // Handle duplicate inspection button click
        $('.duplicate-inspection').click(function() {
            var inspectionId = $(this).data('id');
            var contractNumber = $(this).data('contract');
            var customer = $(this).data('customer');

            $('#inspection-contract').text(contractNumber);
            $('#inspection-customer').text(customer);

            // Store inspection ID for later use
            $('#confirm-duplicate').data('inspection-id', inspectionId);

            // Show modal
            $('#duplicateInspectionModal').modal('show');
        });

        // Handle confirm duplicate button click
        $('#confirm-duplicate').click(function() {
            var inspectionId = $(this).data('inspection-id');
            var scheduledDate = $('#scheduled_date').val();
            var notes = $('#duplicate-notes').val();

            if (!scheduledDate) {
                showErrorToast('{% trans "الرجاء تحديد تاريخ التنفيذ الجديد" %}');
                return;
            }

            // Show loading spinner
            $('.modal-loading').removeClass('d-none');
            $('.modal-footer').addClass('d-none');
            $('.modal-body').addClass('d-none');

            // Send AJAX request to duplicate inspection
            $.ajax({
                url: '{% url "inspections:ajax_duplicate_inspection" %}',
                type: 'POST',
                data: {
                    'inspection_id': inspectionId,
                    'scheduled_date': scheduledDate,
                    'additional_notes': notes,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Close modal
                        $('#duplicateInspectionModal').modal('hide');

                        // Show success toast
                        showSuccessToast('{% trans "تم تكرار المعاينة بنجاح" %}');

                        // Reload page after a short delay to show new inspection
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // Show error toast
                        showErrorToast('{% trans "حدث خطأ أثناء تكرار المعاينة" %}: ' + response.error);

                        // Hide loading, show buttons
                        $('.modal-loading').addClass('d-none');
                        $('.modal-footer').removeClass('d-none');
                        $('.modal-body').removeClass('d-none');
                    }
                },
                error: function() {
                    // Show error toast
                    showErrorToast('{% trans "حدث خطأ أثناء الاتصال بالخادم" %}');

                    // Hide loading, show buttons
                    $('.modal-loading').addClass('d-none');
                    $('.modal-footer').removeClass('d-none');
                    $('.modal-body').removeClass('d-none');
                }
            });
        });

        // Functions for showing toast notifications
        function showSuccessToast(message) {
            $('#successToastMessage').text(message);
            var toast = new bootstrap.Toast($('#successToast'));
            toast.show();
        }

        function showErrorToast(message) {
            $('#errorToastMessage').text(message);
            var toast = new bootstrap.Toast($('#errorToast'));
            toast.show();
        }
    });
</script>
{% endblock %}
