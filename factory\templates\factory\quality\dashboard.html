{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">لوحة تحكم الجودة</h5>
                <a href="{% url 'factory:quality_check_list' %}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-list"></i> عرض جميع الفحوصات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quality Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clipboard-check fa-2x mb-2" style="color: var(--primary);"></i>
                <h5 class="card-title">إجمالي الفحوصات</h5>
                <p class="display-6">{{ total_checks }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                <h5 class="card-title">الفحوصات الناجحة</h5>
                <p class="display-6">{{ passed_checks }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x mb-2 text-danger"></i>
                <h5 class="card-title">الفحوصات الفاشلة</h5>
                <p class="display-6">{{ failed_checks }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-sync fa-2x mb-2" style="color: var(--warning);"></i>
                <h5 class="card-title">يحتاج إعادة عمل</h5>
                <p class="display-6">{{ rework_needed }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Pass Rate Progress -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">معدل النجاح</h5>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ pass_rate }}%;" 
                         aria-valuenow="{{ pass_rate }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        {{ pass_rate|floatformat:1 }}%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Quality Checks -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">آخر فحوصات الجودة</h5>
            </div>
            <div class="card-body">
                {% if recent_checks %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>تاريخ الفحص</th>
                                <th>القائم بالفحص</th>
                                <th>النتيجة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in recent_checks %}
                            <tr>
                                <td>{{ check.production_order.order.order_number }}</td>
                                <td>{{ check.check_date|date:"Y-m-d" }}</td>
                                <td>{{ check.checked_by.get_full_name }}</td>
                                <td>
                                    <span class="badge {% if check.result == 'passed' %}bg-success{% elif check.result == 'failed' %}bg-danger{% else %}bg-warning{% endif %}">
                                        {{ check.get_result_display }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'factory:quality_check_detail' check.pk %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">لا توجد فحوصات جودة حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
