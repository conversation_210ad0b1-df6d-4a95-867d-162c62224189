# ===================================
# CRM System - Clean Requirements
# تم تنظيف وتحسين المتطلبات
# ===================================

# Core Django Framework
Django>=4.2.0,<5.0
djangorestframework==3.14.0
djangorestframework-simplejwt==5.3.0

# Database
psycopg2>=2.9.9
dj-database-url>=2.0.0

# UI and Forms
django-widget-tweaks>=1.4.12
django-model-utils>=4.3.1
django-cors-headers==4.3.1

# Environment and Configuration
django-environ==0.12.0
python-dotenv==1.0.0

# Task Scheduling
django-apscheduler==0.6.2

# File Handling and Import/Export
django-import-export==3.3.1
openpyxl==3.1.5
pillow==11.2.1
python-magic==0.4.27
django-dbbackup==4.2.1

# Google Services Integration
google-auth==2.39.0
google-api-python-client==2.169.0
google-auth-httplib2==0.2.0
google-auth-oauthlib>=0.4.6

# System Monitoring
psutil>=5.9.0

# Security
cryptography==41.0.1
PyJWT==2.10.1

# Production Server
gunicorn==21.2.0
whitenoise==6.5.0

# Utilities
pytz==2025.2
python-dateutil==2.9.0.post0
PyYAML==6.0.2

# ASGI Support (minimal)
asgiref==3.8.1

# ===================================
# Removed Libraries (for reference):
# ===================================
# numpy>=1.24.0          # Data analysis - not used
# pandas>=2.2.3          # Data analysis - not used  
# scikit-learn>=1.2.0    # Machine learning - not used
# plotly>=5.13.0         # Plotting - not used
# channels==4.0.0        # WebSocket - not used
# channels-redis==4.1.0  # WebSocket - not used
# redis==5.0.1           # Caching - not used
# daphne==4.0.0          # ASGI server - not used
# boto3>=1.26.0          # AWS services - not used
