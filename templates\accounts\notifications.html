{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}الإشعارات - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{% trans "الإشعارات" %}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-bell"></i> {% trans "الإشعارات" %}</h2>
        <div>
            {% if page_obj %}
            <button id="markAllReadBtn" class="btn btn-outline-primary me-2">
                <i class="fas fa-check-double"></i> {% trans "تحديد الكل كمقروء" %}
            </button>
            {% endif %}
            <div class="btn-group">
                <a href="?filter=all" class="btn btn-outline-secondary {% if filter_type == 'all' or not filter_type %}active{% endif %}">
                    <i class="fas fa-list"></i> {% trans "الكل" %}
                </a>
                <a href="?filter=unread" class="btn btn-outline-secondary {% if filter_type == 'unread' %}active{% endif %}">
                    <i class="fas fa-envelope"></i> {% trans "غير مقروءة" %}
                </a>
                <a href="?filter=read" class="btn btn-outline-secondary {% if filter_type == 'read' %}active{% endif %}">
                    <i class="fas fa-envelope-open"></i> {% trans "مقروءة" %}
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="list-group">
                    {% for notification in page_obj %}
                        <a href="{% url 'accounts:notification_detail' notification.id %}" class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-light{% endif %}">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">
                                        {% if notification.priority == 'urgent' %}
                                            <span class="badge bg-danger">{% trans "عاجل" %}</span>
                                        {% elif notification.priority == 'high' %}
                                            <span class="badge bg-warning text-dark">{% trans "مهم" %}</span>
                                        {% elif notification.priority == 'medium' %}
                                            <span class="badge bg-info text-dark">{% trans "متوسط" %}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "عادي" %}</span>
                                        {% endif %}
                                        {{ notification.title }}
                                    </h5>
                                    <p class="mb-1">{{ notification.message|truncatechars:150 }}</p>
                                    <small>
                                        <i class="fas fa-user"></i> {{ notification.sender.get_full_name|default:notification.sender.username }} 
                                        ({{ notification.sender_department.name }})
                                        {% if notification.target_branch %}
                                        <span class="mx-2">|</span>
                                        <i class="fas fa-building"></i> {{ notification.target_branch.name }}
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ notification.created_at|date:"Y-m-d H:i" }}</small>
                                    {% if not notification.is_read %}
                                        <div><span class="badge bg-primary">{% trans "جديد" %}</span></div>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if filter_type %}&filter={{ filter_type }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                            {% else %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}{% if filter_type %}&filter={{ filter_type }}{% endif %}">{{ i }}</a></li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if filter_type %}&filter={{ filter_type }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> {% trans "لا توجد إشعارات" %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const markAllReadBtn = document.getElementById('markAllReadBtn');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) {
                    // Send AJAX request to mark all notifications as read
                    fetch('{% url "accounts:mark_all_notifications_read" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': '{{ csrf_token }}',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({})
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload the page to show updated status
                            window.location.reload();
                        } else {
                            alert('حدث خطأ أثناء تحديد الإشعارات كمقروءة.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء تحديد الإشعارات كمقروءة.');
                    });
                }
            });
        }
    });
</script>
{% endblock %}
