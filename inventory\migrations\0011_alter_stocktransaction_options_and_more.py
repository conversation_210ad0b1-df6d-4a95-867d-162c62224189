# Generated by Django 4.2.21 on 2025-06-12 11:35

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0010_alter_stocktransaction_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='stocktransaction',
            options={'ordering': ['-transaction_date', '-date'], 'verbose_name': 'حركة مخزون', 'verbose_name_plural': 'حركات المخزون'},
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='date',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='running_balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد المتحرك'),
        ),
        migrations.AlterField(
            model_name='stocktransaction',
            name='transaction_date',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ العملية'),
        ),
    ]
