{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">المصنع</a></li>
            <li class="breadcrumb-item"><a href="{% url 'factory:production_issue_list' %}">مشاكل الإنتاج</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ issue.title }}</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">
                {% if issue.severity == 'critical' %}
                    <span class="badge bg-danger">حرجة</span>
                {% elif issue.severity == 'high' %}
                    <span class="badge bg-warning text-dark">عالية</span>
                {% elif issue.severity == 'medium' %}
                    <span class="badge bg-info text-dark">متوسطة</span>
                {% else %}
                    <span class="badge bg-secondary">منخفضة</span>
                {% endif %}
                {{ issue.title }}
            </h4>
            <div>
                <a href="{% url 'factory:production_issue_update' issue.id %}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{% url 'factory:production_order_detail' issue.production_order.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة لأمر الإنتاج
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>معلومات المشكلة</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">أمر الإنتاج</th>
                            <td>
                                <a href="{% url 'factory:production_order_detail' issue.production_order.id %}">
                                    {{ issue.production_order.order.order_number }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>خط الإنتاج</th>
                            <td>{{ issue.production_order.production_line.name }}</td>
                        </tr>
                        <tr>
                            <th>الخطورة</th>
                            <td>
                                {% if issue.severity == 'critical' %}
                                    <span class="badge bg-danger">حرجة</span>
                                {% elif issue.severity == 'high' %}
                                    <span class="badge bg-warning text-dark">عالية</span>
                                {% elif issue.severity == 'medium' %}
                                    <span class="badge bg-info text-dark">متوسطة</span>
                                {% else %}
                                    <span class="badge bg-secondary">منخفضة</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                {% if issue.resolved %}
                                    <span class="badge bg-success">تم الحل</span>
                                {% else %}
                                    <span class="badge bg-danger">غير محلولة</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>معلومات التقرير</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">تم الإبلاغ بواسطة</th>
                            <td>{{ issue.reported_by.get_full_name|default:issue.reported_by.username }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإبلاغ</th>
                            <td>{{ issue.reported_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        {% if issue.resolved %}
                        <tr>
                            <th>تم الحل بواسطة</th>
                            <td>{{ issue.resolved_by.get_full_name|default:issue.resolved_by.username }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الحل</th>
                            <td>{{ issue.resolved_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">وصف المشكلة</h5>
                </div>
                <div class="card-body">
                    <p>{{ issue.description|linebreaks }}</p>
                </div>
            </div>
            
            {% if issue.resolved %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">ملاحظات الحل</h5>
                </div>
                <div class="card-body">
                    <p>{{ issue.resolution_notes|linebreaks|default:"لا توجد ملاحظات" }}</p>
                </div>
            </div>
            {% endif %}
            
            {% if not issue.resolved %}
            <div class="mt-4">
                <a href="{% url 'factory:production_issue_update' issue.id %}" class="btn btn-success">
                    <i class="fas fa-check"></i> تحديد كمحلولة
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
