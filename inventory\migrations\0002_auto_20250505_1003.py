# Generated by Django 3.2.25 on 2025-05-05 07:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['product'], name='inv_adj_product_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['batch'], name='inv_adj_batch_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['adjustment_type'], name='inv_adj_type_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['date'], name='inv_adj_date_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['created_by'], name='inv_adj_creator_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name'], name='product_name_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['code'], name='product_code_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category'], name='product_category_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['price'], name='product_price_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at'], name='product_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['product'], name='product_batch_product_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['location'], name='product_batch_location_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['expiry_date'], name='product_batch_expiry_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['barcode'], name='product_batch_barcode_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['created_at'], name='product_batch_created_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['supplier'], name='purchase_order_supplier_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['warehouse'], name='purchase_order_warehouse_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['status'], name='purchase_order_status_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['order_date'], name='purchase_order_date_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['expected_date'], name='purchase_order_expected_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorderitem',
            index=models.Index(fields=['purchase_order'], name='po_item_order_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorderitem',
            index=models.Index(fields=['product'], name='po_item_product_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorderitem',
            index=models.Index(fields=['received_quantity'], name='po_item_received_idx'),
        ),
        migrations.AddIndex(
            model_name='stockalert',
            index=models.Index(fields=['product'], name='stock_alert_product_idx'),
        ),
        migrations.AddIndex(
            model_name='stockalert',
            index=models.Index(fields=['alert_type'], name='stock_alert_type_idx'),
        ),
        migrations.AddIndex(
            model_name='stockalert',
            index=models.Index(fields=['status'], name='stock_alert_status_idx'),
        ),
        migrations.AddIndex(
            model_name='stockalert',
            index=models.Index(fields=['created_at'], name='stock_alert_created_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['product'], name='stock_trans_product_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_type'], name='stock_trans_type_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['date'], name='stock_trans_date_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['created_by'], name='stock_trans_creator_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['product', 'transaction_type'], name='stock_trans_prod_type_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['name'], name='warehouse_name_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['code'], name='warehouse_code_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['branch'], name='warehouse_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['is_active'], name='warehouse_active_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['manager'], name='warehouse_manager_idx'),
        ),
    ]
