# Generated by Django 3.2.25 on 2025-05-04 08:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='inventory.category', verbose_name='الفئة الأب')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('unit', models.CharField(choices=[('piece', 'قطعة'), ('meter', 'متر'), ('sqm', 'متر مربع'), ('kg', 'كيلوجرام')], default='piece', max_length=20, verbose_name='الوحدة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='السعر')),
                ('minimum_stock', models.PositiveIntegerField(default=0, verbose_name='الحد الأدنى للمخزون')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('expected_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التوريد المتوقع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('ordered', 'تم الطلب'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders_created', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'طلب شراء',
                'verbose_name_plural': 'طلبات الشراء',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='StockTransactionReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='السبب')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'سبب حركة المخزون',
                'verbose_name_plural': 'أسباب حركات المخزون',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(max_length=100, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المخزن')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouses', to='accounts.branch', verbose_name='الفرع')),
                ('manager', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='المدير المسؤول')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموقع')),
                ('code', models.CharField(max_length=20, verbose_name='كود الموقع')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='inventory.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'موقع في المخزن',
                'verbose_name_plural': 'مواقع المخزن',
                'ordering': ['warehouse', 'name'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', 'وارد'), ('out', 'صادر')], max_length=3, verbose_name='نوع الحركة')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='التاريخ')),
                ('reference', models.CharField(blank=True, max_length=100, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_transactions', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='inventory.product', verbose_name='المنتج')),
                ('reason', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stocktransactionreason', verbose_name='السبب')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('out_of_stock', 'نفاد المخزون'), ('expiry', 'قرب انتهاء الصلاحية')], max_length=20, verbose_name='نوع التنبيه')),
                ('status', models.CharField(choices=[('new', 'جديد'), ('in_progress', 'قيد المعالجة'), ('resolved', 'تم الحل'), ('ignored', 'تم التجاهل')], default='new', max_length=20, verbose_name='الحالة')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_alerts', to='inventory.product', verbose_name='المنتج')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_stock_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
            ],
            options={
                'verbose_name': 'تنبيه مخزون',
                'verbose_name_plural': 'تنبيهات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('received_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المستلمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_order_items', to='inventory.product', verbose_name='المنتج')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.purchaseorder', verbose_name='طلب الشراء')),
            ],
            options={
                'verbose_name': 'عنصر طلب شراء',
                'verbose_name_plural': 'عناصر طلبات الشراء',
            },
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='inventory.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='warehouse',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders', to='inventory.warehouse', verbose_name='المخزن المستلم'),
        ),
        migrations.CreateModel(
            name='ProductBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50, verbose_name='رقم الدفعة')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('manufacturing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التصنيع')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر التكلفة')),
                ('barcode', models.CharField(blank=True, max_length=100, verbose_name='الباركود')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('location', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_batches', to='inventory.warehouselocation', verbose_name='الموقع')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'دفعة منتج',
                'verbose_name_plural': 'دفعات المنتجات',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'batch_number')},
            },
        ),
        migrations.CreateModel(
            name='InventoryAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_type', models.CharField(choices=[('count', 'جرد'), ('damage', 'تلف'), ('loss', 'فقدان'), ('return', 'مرتجع'), ('other', 'أخرى')], max_length=10, verbose_name='نوع التسوية')),
                ('quantity_before', models.PositiveIntegerField(verbose_name='الكمية قبل التسوية')),
                ('quantity_after', models.PositiveIntegerField(verbose_name='الكمية بعد التسوية')),
                ('reason', models.TextField(verbose_name='سبب التسوية')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسوية')),
                ('batch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments', to='inventory.productbatch', verbose_name='الدفعة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustments', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-date'],
            },
        ),
    ]
