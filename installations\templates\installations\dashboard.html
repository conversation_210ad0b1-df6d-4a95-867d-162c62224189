{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة تحكم التركيبات - احترافية" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-tools"></i> لوحة تحكم التركيبات</h1>
        <div>
            <a href="{% url 'installations:installation_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> تركيب جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white mb-3">
                <div class="card-body">
                    <h6 class="card-title">التركيبات المعلقة</h6>
                    <h2 class="mb-0">{{ pending_installations_count }}</h2>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>بانتظار الجدولة</span>
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white mb-3">
                <div class="card-body">
                    <h6 class="card-title">التركيبات قيد التنفيذ</h6>
                    <h2 class="mb-0">{{ in_progress_installations_count }}</h2>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>قيد التنفيذ</span>
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">تركيبات متأخرة</h6>
                            <h2 class="mb-0">{{ overdue_installations_count }}</h2>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">تركيبات مكتملة</h6>
                            <h2 class="mb-0">{{ completed_installations_count }}</h2>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Installations -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hammer"></i> آخر التركيبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>رقم التركيب</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>فريق التركيب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for installation in recent_installations %}
                                <tr>
                                    <td>#{{ installation.id }}</td>
                                    <td>{{ installation.order.customer.name }}</td>
                                    <td>{{ installation.scheduled_date|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if installation.status == 'pending' %}
                                            <span class="badge bg-warning">{{ installation.get_status_display }}</span>
                                        {% elif installation.status == 'scheduled' %}
                                            <span class="badge bg-info">{{ installation.get_status_display }}</span>
                                        {% elif installation.status == 'in_progress' %}
                                            <span class="badge bg-primary">{{ installation.get_status_display }}</span>
                                        {% elif installation.status == 'completed' %}
                                            <span class="badge bg-success">{{ installation.get_status_display }}</span>
                                        {% elif installation.status == 'cancelled' %}
                                            <span class="badge bg-danger">{{ installation.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if installation.team %}
                                            {{ installation.team.name }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد تركيبات حديثة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Quality Checks -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-check"></i> فحوصات الجودة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for check in recent_quality_checks %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">تركيب #{{ check.installation.id }}</h6>
                                <small class="text-muted">{{ check.created_at|date:"Y-m-d" }}</small>
                            </div>
                            <p class="mb-1">{{ check.get_criteria_display }}</p>
                            <div class="star-rating">
                                {% for i in "12345"|make_list %}
                                    {% if forloop.counter <= check.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center p-3">
                            لا توجد فحوصات جودة حديثة
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Installation Progress Chart -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> توزيع حالات التركيب
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="installationStats" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

{{ pending_installations_count|default_if_none:0|json_script:"pendingCount" }}
{{ completed_installations_count|default_if_none:0|json_script:"completedCount" }}
{{ in_progress_installations_count|default_if_none:0|json_script:"inProgressCount" }}
{{ overdue_installations_count|default_if_none:0|json_script:"overdueCount" }}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chartData = {
        pendingCount: JSON.parse(document.getElementById('pendingCount').textContent),
        completedCount: JSON.parse(document.getElementById('completedCount').textContent),
        inProgressCount: JSON.parse(document.getElementById('inProgressCount').textContent),
        overdueCount: JSON.parse(document.getElementById('overdueCount').textContent)
    };

    const ctx = document.getElementById('installationStats').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['معلقة', 'مكتملة', 'قيد التنفيذ', 'متأخرة'],
            datasets: [{
                data: [
                    chartData.pendingCount,
                    chartData.completedCount,
                    chartData.inProgressCount,
                    chartData.overdueCount
                ],
                backgroundColor: [
                    '#ffc107',  // warning for pending
                    '#198754',  // success for completed
                    '#0d6efd',  // primary for in_progress
                    '#dc3545'   // danger for overdue
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
{% endblock %}
