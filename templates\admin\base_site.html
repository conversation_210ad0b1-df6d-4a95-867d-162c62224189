{% extends "admin/base.html" %}
{% load i18n %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block usertools %}
<div id="user-tools">
    {% if user.is_authenticated %}
        {% block welcome-msg %}
            <strong>{% firstof user.get_short_name user.get_username %}</strong>.
        {% endblock %}
        {% block userlinks %}
            <!-- Back to site link -->
            <a href="{% url 'home' %}">{% translate 'View site' %}</a> /
            {% if user.is_active and user.is_staff %}
                {% url 'django-admindocs-docroot' as docsroot %}
                {% if docsroot %}
                    <a href="{{ docsroot }}">{% translate 'Documentation' %}</a> /
                {% endif %}
            {% endif %}
            {% if user.has_usable_password %}
                <a href="{% url 'admin:password_change' %}">{% translate 'Change password' %}</a> /
            {% endif %}
            <a href="{% url 'admin:logout' %}">{% translate 'Log out' %}</a>
        {% endblock %}
    {% endif %}
</div>
{% endblock %}