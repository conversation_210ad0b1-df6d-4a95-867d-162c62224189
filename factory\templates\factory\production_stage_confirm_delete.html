{% extends 'base.html' %}

{% block title %}حذف مرحلة الإنتاج: {{ stage.name }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">حذف مرحلة الإنتاج</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_list' %}">أوامر الإنتاج</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_detail' production_order.pk %}">{{ production_order.order.order_number }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حذف مرحلة الإنتاج</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى أمر الإنتاج
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--alert);">
                <div class="card-header" style="background-color: var(--alert); color: white;">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> تأكيد الحذف</h5>
                </div>
                <div class="card-body text-center">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h4>هل أنت متأكد من حذف مرحلة الإنتاج "{{ stage.name }}"؟</h4>
                        <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه، وسيؤدي إلى حذف جميع البيانات المرتبطة بهذه المرحلة.</p>
                    </div>
                    
                    <div class="card mb-4" style="border-color: var(--neutral);">
                        <div class="card-header" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <h6 class="mb-0">معلومات مرحلة الإنتاج</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%;">اسم المرحلة</th>
                                    <td>{{ stage.name }}</td>
                                </tr>
                                <tr>
                                    <th>أمر الإنتاج</th>
                                    <td>{{ production_order.order.order_number }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ البدء</th>
                                    <td>{{ stage.start_date|date:"Y-m-d"|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ الانتهاء</th>
                                    <td>{{ stage.end_date|date:"Y-m-d"|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <span class="badge {% if stage.completed %}bg-success{% else %}bg-secondary{% endif %}">
                                            {% if stage.completed %}مكتملة{% else %}قيد التنفيذ{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>المسؤول</th>
                                    <td>{{ stage.assigned_to.get_full_name|default:stage.assigned_to.username|default:"-" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn" style="background-color: var(--alert); color: white;">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                        <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
